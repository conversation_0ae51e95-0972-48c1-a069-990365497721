"""
Test create_reservation with valid IDs found in existing data.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_with_valid_ids():
    """Test create_reservation with valid IDs from existing data."""
    print("🎯 Testing Create Reservation with Valid IDs...")
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Use valid IDs found in existing data
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Test with Garden Junior room type (ID: 653498) - found in existing reservation
        valid_reservation = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "guestCountry": "US",  # Use US instead of FR
            "startDate": tomorrow,
            "endDate": day_after,
            "roomTypeID": "653498",  # Garden Junior - confirmed valid
            "adults": 2,
            "children": 0,
            "sourceID": "s-1",  # Website/Booking Engine - found in existing data
            "paymentMethod": "cash"
        }
        
        print(f"\n📋 Testing with Valid Reservation Data:")
        for key, value in valid_reservation.items():
            print(f"   {key}: {value}")
        
        try:
            print(f"\n🚀 Calling create_reservation_tool...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": valid_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success') is False:
                    print(f"\n❌ API Error: {response_data.get('message')}")
                    
                    # If still getting parameter format error, let's try with minimal data
                    if "Invalid Parameter Format" in response_data.get('message', ''):
                        print(f"\n🔄 Trying with minimal required parameters...")
                        
                        minimal_reservation = {
                            "firstName": "John",
                            "lastName": "Doe", 
                            "email": f"minimal.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
                            "startDate": tomorrow,
                            "endDate": day_after,
                            "roomTypeID": "653498"
                        }
                        
                        print(f"\n📋 Minimal Data:")
                        for key, value in minimal_reservation.items():
                            print(f"   {key}: {value}")
                        
                        result2 = await client.call_tool("create_reservation_tool", {
                            "reservation_data": minimal_reservation
                        })
                        
                        if result2 and result2[0].text:
                            response_data2 = json.loads(result2[0].text)
                            print(f"\n📊 Minimal Response:")
                            print(json.dumps(response_data2, indent=2))
                else:
                    print(f"\n✅ Success! Response: {response_data}")
                    if 'reservationID' in response_data:
                        print(f"\n🎉 Created reservation ID: {response_data['reservationID']}")
            else:
                print(f"\n⚠️  Empty or invalid response")
                
        except Exception as e:
            print(f"\n💥 Exception: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_with_valid_ids())
