"""
Configuration module for Cloudbeds MCP.

This module loads environment variables for Cloudbeds API credentials.
"""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Cloudbeds API credentials
CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")

# API configuration
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"
API_RATE_LIMIT = 5  # requests per second

# Validate required environment variables
if not CLOUDBEDS_API_KEY:
    raise ValueError("CLOUDBEDS_API_KEY environment variable is required")
