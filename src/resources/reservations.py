"""
Reservation resources for Cloudbeds MCP.

This module provides resources for accessing Cloudbeds reservation data.
"""

from typing import Dict, List, Optional, Any

from src.tools.reservations import get_reservations, get_reservation_details

async def get_recent_reservations(days: int = 30) -> List[Dict[str, Any]]:
    """
    Get recent reservations as a resource.
    
    Args:
        days (int): Number of days to look back
        
    Returns:
        List[Dict[str, Any]]: List of reservation data
    """
    return await get_reservations(days_back=days)

async def get_reservation_by_id(reservation_id: str) -> Dict[str, Any]:
    """
    Get a specific reservation by ID.
    
    Args:
        reservation_id (str): Reservation ID
        
    Returns:
        Dict[str, Any]: Reservation details
    """
    return await get_reservation_details(reservation_id)
