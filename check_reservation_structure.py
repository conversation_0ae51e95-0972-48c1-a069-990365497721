"""
Check the detailed structure of reservation nested objects.
"""

import asyncio
import json
from fastmcp import Client

async def check_reservation_structure():
    """Check the nested structure of reservation objects."""
    print("🔍 Checking Reservation Nested Structure...")
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        try:
            reservation_id = "5113613993769"
            details = await client.call_tool("get_reservation_tool", {"reservation_id": reservation_id})
            if details and details[0].text:
                detail_data = json.loads(details[0].text)
                if detail_data:
                    # Check unassigned rooms
                    print("\n📋 Unassigned Rooms Structure:")
                    unassigned = detail_data.get('unassigned', [])
                    if unassigned:
                        for i, room in enumerate(unassigned):
                            print(f"   Room {i}:")
                            for key, value in room.items():
                                print(f"     {key}: {value}")
                    else:
                        print("   No unassigned rooms")
                    
                    # Check assigned rooms
                    print("\n📋 Assigned Rooms Structure:")
                    assigned = detail_data.get('assigned', [])
                    if assigned:
                        for i, room in enumerate(assigned):
                            print(f"   Room {i}:")
                            for key, value in room.items():
                                print(f"     {key}: {value}")
                    else:
                        print("   No assigned rooms")
                    
                    # Check guest list
                    print("\n👤 Guest List Structure:")
                    guest_list = detail_data.get('guestList', {})
                    if guest_list:
                        for key, value in guest_list.items():
                            if isinstance(value, list):
                                print(f"   {key}: list with {len(value)} items")
                                for i, item in enumerate(value):
                                    print(f"     Item {i}: {item}")
                            else:
                                print(f"   {key}: {value}")
                    else:
                        print("   No guest list data")
                    
                    # Check balance detailed
                    print("\n💰 Balance Detailed Structure:")
                    balance_detailed = detail_data.get('balanceDetailed', {})
                    if balance_detailed:
                        for key, value in balance_detailed.items():
                            print(f"   {key}: {value}")
                    else:
                        print("   No balance detailed data")
                        
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_reservation_structure())
