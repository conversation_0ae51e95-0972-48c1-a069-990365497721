"""
Test MCP connection.

This script tests the connection to the MCP server.
"""

import asyncio
import sys

# Try to import fastmcp
try:
    from fastmcp import Client
except ImportError:
    print("Error: fastmcp package not found. Please install it with 'pip install fastmcp'.")
    sys.exit(1)

async def test_connection(url: str = "http://localhost:8000/mcp"):
    """
    Test the connection to the MCP server.

    Args:
        url (str): URL of the MCP server
    """
    print(f"Testing connection to MCP server at {url}...")

    try:
        # Connect to the MCP server
        async with Client(url) as client:
            # List tools
            tools = await client.list_tools()
            print(f"Successfully connected to MCP server. Found {len(tools)} tools.")

            # Test a tool
            print("\nTesting get_room_types_tool...")
            result = await client.call_tool("get_room_types_tool")
            print(f"Result: {result}")

            return True
    except Exception as e:
        print(f"Error connecting to MCP server: {str(e)}")
        return False

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_connection())
