"""
Reservation prompts for Cloudbeds MCP.

This module provides prompts for working with Cloudbeds reservations.
"""

def summarize_reservation_prompt(reservation_data: dict) -> str:
    """
    Generate a prompt to summarize reservation data.
    
    Args:
        reservation_data (dict): Reservation data
        
    Returns:
        str: Prompt for summarizing reservation
    """
    return f"""
Please summarize the following Cloudbeds reservation information in a concise, easy-to-read format:

Reservation ID: {reservation_data.get('reservationID', 'N/A')}
Guest: {reservation_data.get('guestName', 'N/A')}
Check-in: {reservation_data.get('startDate', 'N/A')}
Check-out: {reservation_data.get('endDate', 'N/A')}
Room: {reservation_data.get('roomName', 'N/A')}
Status: {reservation_data.get('status', 'N/A')}

Include any special requests or important notes that staff should be aware of.
"""

def create_reservation_prompt() -> str:
    """
    Generate a prompt for creating a new reservation.
    
    Returns:
        str: Prompt for creating a reservation
    """
    return """
Please help me create a new reservation in Cloudbeds. I need the following information:

1. Guest name
2. Guest email
3. Guest phone
4. Check-in date (YYYY-MM-DD)
5. Check-out date (YYYY-MM-DD)
6. Number of adults
7. Number of children
8. Room type or specific room
9. Rate plan
10. Any special requests or notes

Please format this information in a structured way that can be used to create the reservation.
"""
