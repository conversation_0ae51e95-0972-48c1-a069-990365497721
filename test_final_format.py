"""
Test the final correct format for reservation creation.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_final_format():
    """Test the final correct format."""
    print("🎯 Testing Final Correct Format...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Test with both rooms parameter AND top-level adults/children
    print("\n📡 Final Test: Rooms + Top-level adults/children")
    try:
        rooms_json = json.dumps([{
            "roomTypeID": "653498",
            "adults": "2",
            "children": "0"
        }])
        
        final_data = {
            "propertyID": CLOUDBEDS_PROPERTY_ID,
            "guestFirstName": "<PERSON>",
            "guestLastName": "Doe",
            "guestEmail": f"test.final.{int(asyncio.get_event_loop().time())}@example.com",
            "guestCountry": "US",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498",
            "adults": "2",  # Top-level adults
            "children": "0",  # Top-level children
            "rooms": rooms_json,  # Rooms array
            "sourceID": "s-1",  # Website/Booking Engine
            "paymentMethod": "cash"
        }
        
        print(f"   📋 Final data structure:")
        for key, value in final_data.items():
            if key == "rooms":
                print(f"     {key}: {value}")
            else:
                print(f"     {key}: {value}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=final_data, headers=headers)
            print(f"\n   📡 Response:")
            print(f"     Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"     Success: {data.get('success')}")
                print(f"     Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"\n   🎉 SUCCESS! Reservation created!")
                    if 'reservationID' in data:
                        print(f"     Reservation ID: {data['reservationID']}")
                    print(f"     Full response: {json.dumps(data, indent=2)}")
                else:
                    print(f"\n   ❌ Still failed: {data.get('message')}")
                    
                    # If it's still a parameter issue, let's try without some optional fields
                    if "Parameter" in data.get('message', ''):
                        print(f"\n   🔄 Trying without optional fields...")
                        
                        minimal_data = {
                            "propertyID": CLOUDBEDS_PROPERTY_ID,
                            "guestFirstName": "John",
                            "guestLastName": "Doe", 
                            "guestEmail": f"minimal.{int(asyncio.get_event_loop().time())}@example.com",
                            "startDate": "2025-05-26",
                            "endDate": "2025-05-27",
                            "roomTypeID": "653498",
                            "adults": "2",
                            "children": "0",
                            "rooms": rooms_json
                        }
                        
                        response2 = await client.post(f"{API_BASE_URL}/postReservation", data=minimal_data, headers=headers)
                        if response2.status_code == 200:
                            data2 = response2.json()
                            print(f"     Minimal Success: {data2.get('success')}")
                            print(f"     Minimal Message: {data2.get('message')}")
                            if data2.get('success'):
                                print(f"     🎉 SUCCESS with minimal data!")
            else:
                print(f"     HTTP Error: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_final_format())
