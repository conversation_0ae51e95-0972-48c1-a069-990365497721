"""
Test with the correct payment method values from the API.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_correct_payment_methods():
    """Test with the correct payment method values."""
    print("🎯 Testing with Correct Payment Method Values...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base working parameters
    base_data = {
        "propertyID": "317353",
        "guestFirstName": "John",
        "guestLastName": "Doe",
        "guestEmail": f"success.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])
    }
    
    # Test with correct payment method values from the API
    correct_payment_methods = [
        "credit",  # Main method
        "cards",   # Code
        "visa",    # Card type
        "master"   # Card type (Mastercard)
    ]
    
    for payment_method in correct_payment_methods:
        print(f"\n📡 Testing paymentMethod: '{payment_method}'")
        try:
            test_data = base_data.copy()
            test_data["paymentMethod"] = payment_method
            test_data["guestEmail"] = f"success.{payment_method}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Payment method '{payment_method}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        print(f"   Full response: {json.dumps(data, indent=2)}")
                        
                        # Verify the reservation was created by getting its details
                        print(f"\n   🔍 Verifying reservation creation...")
                        try:
                            verify_headers = {
                                "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
                                "Content-Type": "application/json"
                            }
                            verify_params = {
                                "reservationID": data['reservationID'],
                                "propertyID": "317353"
                            }
                            verify_response = await client.get(f"{API_BASE_URL}/getReservation", params=verify_params, headers=verify_headers)
                            if verify_response.status_code == 200:
                                verify_data = verify_response.json()
                                if verify_data.get('success'):
                                    print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                                else:
                                    print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                        except Exception as ve:
                            print(f"   ⚠️  Verification error: {ve}")
                        
                        return payment_method
                    else:
                        print(f"   ❌ Failed: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No working payment method found")
    return None

if __name__ == "__main__":
    asyncio.run(test_correct_payment_methods())
