"""
Test with the correct paymentMethod values: 'credit' and 'noPayment'
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_correct_payment_methods():
    """Test with the correct payment method values."""
    print("🎯 Testing with Correct Payment Method Values")
    print("=" * 70)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Test 1: With 'noPayment' (default)
        print(f"\n📋 Test 1: Using paymentMethod='noPayment'")
        
        reservation_no_payment = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"nopayment.test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34123456789",
            "address": "Calle Mayor 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0,
            "status": "confirmed",
            "sendEmailConfirmation": True,
            "sourceID": "s-2-1",
            "paymentMethod": "noPayment",  # Correct value!
            "thirdPartyIdentifier": f"mcp-nopay-{int(datetime.now().timestamp())}"
        }
        
        try:
            print(f"\n🚀 Creating reservation with paymentMethod='noPayment'...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_no_payment
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created with 'noPayment'!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    
                    # Verify the reservation
                    if response_data.get('reservationID'):
                        print(f"\n🔍 Verifying reservation...")
                        try:
                            verify_result = await client.call_tool("get_reservation_tool", {
                                "reservation_id": response_data['reservationID']
                            })
                            if verify_result and verify_result[0].text:
                                verify_data = json.loads(verify_result[0].text)
                                if verify_data.get('success') is not False:
                                    print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                                else:
                                    print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                        except Exception as ve:
                            print(f"   ⚠️  Verification error: {ve}")
                    
                    return True
                else:
                    print(f"\n❌ Failed with 'noPayment': {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with 'noPayment': {str(e)}")
        
        # Test 2: With 'credit'
        print(f"\n" + "=" * 70)
        print(f"📋 Test 2: Using paymentMethod='credit'")
        
        reservation_credit = {
            "firstName": "Jane",
            "lastName": "Smith",
            "email": f"credit.test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34987654321",
            "address": "Gran Via 456",
            "city": "Barcelona",
            "country": "ES",
            "postalCode": "08001",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 1,
            "children": 0,
            "status": "confirmed",
            "sendEmailConfirmation": True,
            "sourceID": "s-2-1",
            "paymentMethod": "credit",  # Correct value!
            "thirdPartyIdentifier": f"mcp-credit-{int(datetime.now().timestamp())}"
        }
        
        try:
            print(f"\n🚀 Creating reservation with paymentMethod='credit'...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_credit
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created with 'credit'!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    return True
                else:
                    print(f"\n❌ Failed with 'credit': {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with 'credit': {str(e)}")
        
        # Test 3: Without paymentMethod (should default to 'noPayment')
        print(f"\n" + "=" * 70)
        print(f"📋 Test 3: Without paymentMethod (should default to 'noPayment')")
        
        reservation_default = {
            "firstName": "Carlos",
            "lastName": "Rodriguez",
            "email": f"default.test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 1
        }
        
        try:
            print(f"\n🚀 Creating reservation without paymentMethod...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_default
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created with default payment method!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    return True
                else:
                    print(f"\n📝 Default payment method result: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with default: {str(e)}")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_correct_payment_methods())
    if success:
        print(f"\n🎉 PAYMENT METHOD ISSUE RESOLVED!")
        print(f"🚀 RESERVATION CREATION IS NOW FULLY WORKING!")
    else:
        print(f"\n🔧 STILL INVESTIGATING PAYMENT METHOD ISSUE")
