"""
Test API connection and verify request format.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_api_connection():
    """Test direct API connection to verify format."""
    print("🔍 Testing Direct API Connection...")

    # Test 1: Verify we can make a simple GET request
    print("\n📡 Test 1: Simple GET request (getHotels)")
    try:
        headers = {
            "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
            "Content-Type": "application/json"
        }

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{API_BASE_URL}/getHotels", headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                if data.get('success'):
                    print(f"   ✅ API connection working")
                else:
                    print(f"   ❌ API error: {data.get('message')}")
            else:
                print(f"   ❌ HTTP error: {response.text}")
    except Exception as e:
        print(f"   💥 Exception: {e}")

    # Test 2: Try a minimal POST request to see what format is expected
    print("\n📡 Test 2: Minimal POST request format test")
    try:
        headers = {
            "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
            "Content-Type": "application/x-www-form-urlencoded"
        }

        # Try the absolute minimal data
        minimal_data = {
            "propertyID": CLOUDBEDS_PROPERTY_ID,
            "guestFirstName": "Test",
            "guestLastName": "User",
            "guestEmail": "<EMAIL>",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498"
        }

        print(f"   Sending data: {minimal_data}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{API_BASE_URL}/postReservation",
                data=minimal_data,
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.text}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Parsed response: {json.dumps(data, indent=2)}")
                except:
                    print(f"   Raw response: {response.text}")

    except Exception as e:
        print(f"   💥 Exception: {e}")

    # Test 3: Check if we need different parameter names
    print("\n📡 Test 3: Alternative parameter names")
    try:
        # Try with different parameter names that might be expected
        alt_data = {
            "propertyID": CLOUDBEDS_PROPERTY_ID,
            "firstName": "Test",  # Without guest prefix
            "lastName": "User",
            "email": "<EMAIL>",
            "checkInDate": "2025-05-26",  # Alternative date field names
            "checkOutDate": "2025-05-27",
            "roomType": "653498"  # Alternative room field name
        }

        print(f"   Sending alternative data: {alt_data}")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{API_BASE_URL}/postReservation",
                data=alt_data,
                headers=headers
            )
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                except:
                    print(f"   Raw response: {response.text}")

    except Exception as e:
        print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_api_connection())
