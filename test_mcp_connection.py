"""
Comprehensive test suite for Cloudbeds MCP server.

This script systematically tests all MCP tools, resources, and prompts.
"""

import asyncio
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Try to import fastmcp
try:
    from fastmcp import Client
except ImportError:
    print("Error: fastmcp package not found. Please install it with 'pip install fastmcp'.")
    sys.exit(1)

class MCPTester:
    """Comprehensive MCP testing class."""

    def __init__(self, url: str = "http://localhost:8000/mcp"):
        self.url = url
        self.test_results = {
            "tools": {},
            "resources": {},
            "prompts": {},
            "summary": {
                "total_tests": 0,
                "passed": 0,
                "failed": 0,
                "errors": []
            }
        }

    def log_test_result(self, category: str, name: str, success: bool, result: Any = None, error: str = None):
        """Log test result."""
        self.test_results[category][name] = {
            "success": success,
            "result": result if success else None,
            "error": error if not success else None,
            "timestamp": datetime.now().isoformat()
        }

        self.test_results["summary"]["total_tests"] += 1
        if success:
            self.test_results["summary"]["passed"] += 1
            print(f"✅ {category.upper()}: {name} - PASSED")
        else:
            self.test_results["summary"]["failed"] += 1
            self.test_results["summary"]["errors"].append(f"{category}: {name} - {error}")
            print(f"❌ {category.upper()}: {name} - FAILED: {error}")

    async def test_connection(self) -> bool:
        """Test basic connection to MCP server."""
        print(f"🔗 Testing connection to MCP server at {self.url}...")

        try:
            async with Client(self.url) as client:
                # List available tools
                tools = await client.list_tools()
                resources = await client.list_resources()
                prompts = await client.list_prompts()

                print(f"📊 Server Info:")
                print(f"   - Tools: {len(tools)}")
                print(f"   - Resources: {len(resources)}")
                print(f"   - Prompts: {len(prompts)}")

                return True
        except Exception as e:
            print(f"❌ Connection failed: {str(e)}")
            return False

    async def test_tools(self, client: Client):
        """Test all MCP tools."""
        print("\n🔧 Testing MCP Tools...")

        # Test get_room_types_tool
        try:
            result = await client.call_tool("get_room_types_tool")
            self.log_test_result("tools", "get_room_types_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_room_types_tool", False, error=str(e))

        # Test get_rooms_tool
        try:
            result = await client.call_tool("get_rooms_tool")
            self.log_test_result("tools", "get_rooms_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_rooms_tool", False, error=str(e))

        # Test get_reservations_tool
        try:
            result = await client.call_tool("get_reservations_tool", {"days_back": 7})
            self.log_test_result("tools", "get_reservations_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_reservations_tool", False, error=str(e))

        # Test get_availability_tool
        try:
            start_date = datetime.now().strftime('%Y-%m-%d')
            end_date = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')
            result = await client.call_tool("get_availability_tool", {
                "start_date": start_date,
                "end_date": end_date
            })
            self.log_test_result("tools", "get_availability_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_availability_tool", False, error=str(e))

        # Test search_guests_tool
        try:
            result = await client.call_tool("search_guests_tool", {"search_term": "test"})
            self.log_test_result("tools", "search_guests_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "search_guests_tool", False, error=str(e))

        # Test create_reservation_tool (placeholder)
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": {"test": "data"}
            })
            self.log_test_result("tools", "create_reservation_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "create_reservation_tool", False, error=str(e))

        # Test tools that require specific IDs (will likely fail without valid IDs)
        print("\n🔍 Testing tools that require specific IDs (may fail without valid data)...")

        # Test get_reservation_tool
        try:
            result = await client.call_tool("get_reservation_tool", {"reservation_id": "test123"})
            self.log_test_result("tools", "get_reservation_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_reservation_tool", False, error=str(e))

        # Test get_invoice_tool
        try:
            result = await client.call_tool("get_invoice_tool", {"reservation_id": "test123"})
            self.log_test_result("tools", "get_invoice_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_invoice_tool", False, error=str(e))

        # Test get_guest_tool
        try:
            result = await client.call_tool("get_guest_tool", {"guest_id": "test123"})
            self.log_test_result("tools", "get_guest_tool", True, result)
        except Exception as e:
            self.log_test_result("tools", "get_guest_tool", False, error=str(e))

    async def test_resources(self, client: Client):
        """Test all MCP resources."""
        print("\n📚 Testing MCP Resources...")

        # First list all available resources
        try:
            resources = await client.list_resources()
            print(f"   Found {len(resources)} static resources")
            for resource in resources:
                print(f"     - {resource.uri}: {resource.name}")
        except Exception as e:
            print(f"   ❌ Failed to list resources: {e}")

        # List resource templates
        try:
            templates = await client.list_resource_templates()
            print(f"   Found {len(templates)} resource templates")
            for template in templates:
                print(f"     - {template.uriTemplate}: {template.name}")
        except Exception as e:
            print(f"   ❌ Failed to list resource templates: {e}")

        # Test recent reservations resource
        try:
            result = await client.read_resource("cloudbeds://reservations/recent")
            self.log_test_result("resources", "recent_reservations", True, result)
        except Exception as e:
            self.log_test_result("resources", "recent_reservations", False, error=str(e))

        # Test room types resource
        try:
            result = await client.read_resource("cloudbeds://rooms/types")
            self.log_test_result("resources", "room_types", True, result)
        except Exception as e:
            self.log_test_result("resources", "room_types", False, error=str(e))

        # Test rooms resource
        try:
            result = await client.read_resource("cloudbeds://rooms")
            self.log_test_result("resources", "rooms", True, result)
        except Exception as e:
            self.log_test_result("resources", "rooms", False, error=str(e))

        # Test availability resource
        try:
            result = await client.read_resource("cloudbeds://availability/next30days")
            self.log_test_result("resources", "availability_next30days", True, result)
        except Exception as e:
            self.log_test_result("resources", "availability_next30days", False, error=str(e))

        # Test property resource
        try:
            result = await client.read_resource("cloudbeds://property")
            self.log_test_result("resources", "property", True, result)
        except Exception as e:
            self.log_test_result("resources", "property", False, error=str(e))

        # Test specific reservation resource (will likely fail without valid ID)
        print("\n🔍 Testing resources that require specific IDs (may fail without valid data)...")
        try:
            result = await client.read_resource("cloudbeds://reservations/test123")
            self.log_test_result("resources", "specific_reservation", True, result)
        except Exception as e:
            self.log_test_result("resources", "specific_reservation", False, error=str(e))

    async def test_prompts(self, client: Client):
        """Test all MCP prompts."""
        print("\n💬 Testing MCP Prompts...")

        # First list all available prompts
        try:
            prompts = await client.list_prompts()
            print(f"   Found {len(prompts)} prompts")
            for prompt in prompts:
                print(f"     - {prompt.name}: {prompt.description}")
        except Exception as e:
            print(f"   ❌ Failed to list prompts: {e}")

        # Test summarize_reservation prompt
        try:
            import json
            sample_reservation = {
                "reservationID": "12345",
                "guestName": "John Doe",
                "startDate": "2023-06-01",
                "endDate": "2023-06-05",
                "roomName": "Deluxe King",
                "status": "confirmed"
            }
            # Try passing as JSON string first, then as dict if that fails
            try:
                result = await client.get_prompt("summarize_reservation", {"reservation_data": json.dumps(sample_reservation)})
                self.log_test_result("prompts", "summarize_reservation", True, result)
            except Exception:
                # If JSON string fails, try as dict
                result = await client.get_prompt("summarize_reservation", {"reservation_data": sample_reservation})
                self.log_test_result("prompts", "summarize_reservation", True, result)
        except Exception as e:
            self.log_test_result("prompts", "summarize_reservation", False, error=str(e))

        # Test new_reservation_prompt
        try:
            result = await client.get_prompt("new_reservation_prompt")
            self.log_test_result("prompts", "new_reservation_prompt", True, result)
        except Exception as e:
            self.log_test_result("prompts", "new_reservation_prompt", False, error=str(e))

    async def run_comprehensive_tests(self):
        """Run all tests comprehensively."""
        print("🚀 Starting Comprehensive Cloudbeds MCP Testing...")
        print("=" * 60)

        # Test connection first
        if not await self.test_connection():
            print("❌ Cannot proceed with tests - connection failed")
            return False

        try:
            async with Client(self.url) as client:
                # Run all test categories
                await self.test_tools(client)
                await self.test_resources(client)
                await self.test_prompts(client)

                # Print summary
                self.print_summary()

                # Save results to file
                self.save_results()

                return True

        except Exception as e:
            print(f"❌ Error during testing: {str(e)}")
            return False

    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        summary = self.test_results["summary"]
        print(f"Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"Success Rate: {(summary['passed'] / summary['total_tests'] * 100):.1f}%")

        if summary['errors']:
            print(f"\n🔍 Failed Tests:")
            for error in summary['errors']:
                print(f"   - {error}")

        # Category breakdown
        print(f"\n📋 Category Breakdown:")
        for category in ["tools", "resources", "prompts"]:
            category_results = self.test_results[category]
            total = len(category_results)
            passed = sum(1 for r in category_results.values() if r['success'])
            print(f"   {category.upper()}: {passed}/{total} passed")

    def save_results(self):
        """Save test results to JSON file."""
        filename = f"mcp_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(filename, 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            print(f"\n💾 Test results saved to: {filename}")
        except Exception as e:
            print(f"❌ Failed to save results: {str(e)}")

async def main():
    """Main test runner."""
    tester = MCPTester()
    success = await tester.run_comprehensive_tests()

    if success:
        print("\n🎉 Testing completed successfully!")
    else:
        print("\n💥 Testing failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
