"""
Test different formats for the rooms parameter.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_rooms_parameter():
    """Test different formats for the rooms parameter."""
    print("🎯 Testing Different Rooms Parameter Formats")
    print("=" * 60)
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base data without rooms
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"rooms.test.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "ES",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "paymentMethod": "noPayment"
    }
    
    # Different rooms formats to test
    rooms_formats = [
        # Format 1: Current format (causing issues)
        {
            "name": "Current format (full)",
            "rooms": json.dumps([{
                "roomTypeID": "653498",
                "startDate": "2025-05-26",
                "endDate": "2025-05-27",
                "adults": 2,
                "children": 0
            }])
        },
        
        # Format 2: Minimal rooms
        {
            "name": "Minimal rooms",
            "rooms": json.dumps([{"roomTypeID": "653498"}])
        },
        
        # Format 3: String values in rooms
        {
            "name": "String values",
            "rooms": json.dumps([{
                "roomTypeID": "653498",
                "startDate": "2025-05-26",
                "endDate": "2025-05-27",
                "adults": "2",
                "children": "0"
            }])
        },
        
        # Format 4: Without dates in rooms (since they're at top level)
        {
            "name": "Without dates in rooms",
            "rooms": json.dumps([{
                "roomTypeID": "653498",
                "adults": 2,
                "children": 0
            }])
        },
        
        # Format 5: Just room type ID as string
        {
            "name": "Just roomTypeID",
            "rooms": json.dumps(["653498"])
        },
        
        # Format 6: Simple object
        {
            "name": "Simple object",
            "rooms": json.dumps({"roomTypeID": "653498"})
        },
        
        # Format 7: No JSON, just string
        {
            "name": "Plain string",
            "rooms": "653498"
        },
        
        # Format 8: Empty array
        {
            "name": "Empty array",
            "rooms": json.dumps([])
        }
    ]
    
    try:
        async with httpx.AsyncClient() as client:
            for i, room_format in enumerate(rooms_formats, 1):
                print(f"\n📡 Test {i}: {room_format['name']}")
                print(f"   Rooms value: {room_format['rooms']}")
                
                test_data = base_data.copy()
                test_data["rooms"] = room_format["rooms"]
                test_data["guestEmail"] = f"rooms.{i}.{int(asyncio.get_event_loop().time())}@example.com"
                
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"\n🎉 SUCCESS! Format '{room_format['name']}' works!")
                        print(f"   Reservation ID: {data.get('reservationID')}")
                        print(f"   Confirmation Code: {data.get('confirmationCode')}")
                        return room_format
                    elif "Invalid Parameter Format" not in data.get('message', ''):
                        print(f"   ✅ Progress! Different error (not Invalid Parameter Format)")
                        if "paymentMethod" in data.get('message', ''):
                            print(f"   🎯 This format works for rooms! Payment method is the next issue.")
                else:
                    print(f"   HTTP Error: {response.status_code}")
            
            # If none worked, try without rooms parameter entirely but with adults/children at top level
            print(f"\n📡 Final Test: No rooms parameter, adults/children at top level")
            final_test = base_data.copy()
            final_test["adults"] = "2"
            final_test["children"] = "0"
            final_test["guestEmail"] = f"no.rooms.{int(asyncio.get_event_loop().time())}@example.com"
            
            response = await client.post(f"{API_BASE_URL}/postReservation", data=final_test, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"\n🎉 SUCCESS! No rooms parameter needed!")
                    return {"name": "No rooms parameter", "rooms": None}
                elif "rooms is required" not in data.get('message', ''):
                    print(f"   ✅ Progress! No rooms parameter works for structure")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return None

if __name__ == "__main__":
    working_format = asyncio.run(test_rooms_parameter())
    if working_format:
        print(f"\n🎉🎉🎉 FOUND WORKING ROOMS FORMAT! 🎉🎉🎉")
        print(f"🚀 Working format: {working_format['name']}")
        print(f"✅ RESERVATION CREATION SHOULD NOW WORK!")
    else:
        print(f"\n🔧 STILL NEED TO FIND THE RIGHT ROOMS FORMAT")
