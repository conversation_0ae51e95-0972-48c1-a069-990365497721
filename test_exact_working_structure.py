"""
Test the exact working structure from the technical README.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_exact_working_structure():
    """Test with the exact structure from the working implementation."""
    print("🎯 Testing EXACT Working Implementation Structure...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/json"
    }
    
    # EXACT structure from the working implementation README
    reservation_data = {
        "propertyID": "317353",
        "guestData": {
            "firstName": "<PERSON>",
            "lastName": "Doe",
            "email": f"exact.working.{int(asyncio.get_event_loop().time())}@example.com",
            "phone": "+34123456789",
            "address": "Calle Mayor 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001"
        },
        "roomsData": [
            {
                "roomTypeID": "653498",  # Garden Junior - verified valid
                "startDate": "2025-05-26",
                "endDate": "2025-05-27",
                "adults": 2,
                "children": 0,
                "roomName": "Garden Deluxe"
            }
        ],
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-exact-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": True,
        "sourceID": "s-2-1"
    }
    
    print(f"\n📋 EXACT Working Implementation Structure:")
    print(json.dumps(reservation_data, indent=2))
    
    try:
        print(f"\n🚀 Testing exact structure (should give 'Parameter paymentMethod is required')...")
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", json=reservation_data, headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ PERFECT! Structure is correct, just need payment method")
                    
                    # Now test with payment method - try the working implementation approach
                    # The working implementation might not need payment method, or uses a specific value
                    print(f"\n🔄 Testing without payment method requirement...")
                    
                    # Try adding a simple payment method
                    test_data = reservation_data.copy()
                    
                    # Try different approaches for payment method
                    payment_approaches = [
                        # Approach 1: No payment method (maybe it's optional in working implementation)
                        {},
                        # Approach 2: Simple payment method
                        {"paymentMethod": "credit"},
                        # Approach 3: Payment method in guest data
                        {"guestData": {**reservation_data["guestData"], "paymentMethod": "credit"}},
                        # Approach 4: Payment method as object
                        {"paymentMethod": {"type": "credit"}},
                        # Approach 5: Payment info object
                        {"paymentInfo": {"method": "credit"}},
                    ]
                    
                    for i, payment_approach in enumerate(payment_approaches, 1):
                        print(f"\n   📡 Payment Approach {i}: {payment_approach}")
                        
                        test_data = reservation_data.copy()
                        if payment_approach:
                            test_data.update(payment_approach)
                        
                        response2 = await client.post(f"{API_BASE_URL}/postReservation", json=test_data, headers=headers)
                        if response2.status_code == 200:
                            data2 = response2.json()
                            print(f"      Success: {data2.get('success')}")
                            print(f"      Message: {data2.get('message')}")
                            
                            if data2.get('success'):
                                print(f"      🎉 SUCCESS! Payment approach {i} works!")
                                print(f"      Reservation ID: {data2.get('reservationID')}")
                                return True
                            elif "Invalid Parameter Format" not in data2.get('message', ''):
                                print(f"      ✅ Payment approach {i} accepted")
                
                elif data.get('success'):
                    print(f"   🎉 UNEXPECTED SUCCESS! No payment method needed!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    return True
                else:
                    print(f"   ❌ Different error: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_exact_working_structure())
    if success:
        print(f"\n🎉 EXACT WORKING STRUCTURE SUCCESSFUL!")
    else:
        print(f"\n🔧 STILL NEED TO SOLVE PAYMENT METHOD")
