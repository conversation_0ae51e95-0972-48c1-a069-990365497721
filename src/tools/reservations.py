"""
Reservation tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds reservations.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_reservations(
    days_back: int = 30,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation data
    """
    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    return await api_request("getReservations", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_details(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed reservation data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getReservation", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_invoice(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Invoice data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getInvoice", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_reservation(
    reservation_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (dict): Reservation data
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created reservation data
    """
    # This would need to be implemented with a POST request
    # For now, we'll return a placeholder
    return {"message": "Reservation creation not implemented yet"}

async def update_reservation(
    reservation_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (dict): Data to update
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data
    """
    # This would need to be implemented with a PUT request
    # For now, we'll return a placeholder
    return {"message": "Reservation update not implemented yet"}
