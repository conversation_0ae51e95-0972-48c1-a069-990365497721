"""
Test different parameter names for payment method.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_payment_parameter_names():
    """Test different parameter names for payment method."""
    print("💳 Testing Different Payment Parameter Names...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base working parameters
    base_data = {
        "propertyID": "317353",
        "guestFirstName": "John",
        "guestLastName": "Doe",
        "guestEmail": f"payment.param.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])
    }
    
    # Test different parameter names for payment
    payment_params = [
        ("paymentMethod", "credit"),
        ("payment_method", "credit"),
        ("paymentType", "credit"),
        ("payment_type", "credit"),
        ("paymentCode", "credit"),
        ("payment_code", "credit"),
        ("method", "credit"),
        ("methodCode", "credit"),
        ("cardType", "visa"),
        ("card_type", "visa"),
        ("paymentMethodCode", "cards"),
        ("paymentMethodID", "1"),
        ("paymentID", "1"),
        ("payment", "credit"),
        ("paymentOption", "credit"),
        ("billingMethod", "credit"),
        ("chargeMethod", "credit")
    ]
    
    for param_name, param_value in payment_params:
        print(f"\n📡 Testing {param_name}: '{param_value}'")
        try:
            test_data = base_data.copy()
            test_data[param_name] = param_value
            test_data["guestEmail"] = f"payment.{param_name}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Parameter '{param_name}' with value '{param_value}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        return (param_name, param_value)
                    elif "Invalid Parameter Format" not in data.get('message', ''):
                        print(f"   ✅ Parameter '{param_name}' accepted, next error: {data.get('message')}")
                    else:
                        print(f"   ❌ Parameter '{param_name}' causes 'Invalid Parameter Format'")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    # Try without any payment parameter to see if it's actually required
    print(f"\n📡 Testing without any payment parameter:")
    try:
        test_data = base_data.copy()
        test_data["guestEmail"] = f"nopayment.{int(asyncio.get_event_loop().time())}@example.com"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! No payment parameter needed!")
                    return ("none", "none")
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return None

if __name__ == "__main__":
    asyncio.run(test_payment_parameter_names())
