"""
Property resources for Cloudbeds MCP.

This module provides resources for accessing Cloudbeds property data.
"""

from typing import Dict, Any, List

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_property_info() -> Dict[str, Any]:
    """
    Get property information.
    
    Returns:
        Dict[str, Any]: Property information
    """
    return await api_request("getProperty", property_id=CLOUDBEDS_PROPERTY_ID)

async def get_property_settings() -> Dict[str, Any]:
    """
    Get property settings.
    
    Returns:
        Dict[str, Any]: Property settings
    """
    return await api_request("getPropertySettings", property_id=CLOUDBEDS_PROPERTY_ID)
