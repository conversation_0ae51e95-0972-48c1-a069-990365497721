"""
Test by eliminating parameters one by one to find what's causing Invalid Parameter Format.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_parameter_elimination():
    """Test by eliminating parameters to find the culprit."""
    print("🎯 Parameter Elimination Test")
    print("=" * 50)
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Start with minimal working data (gets to paymentMethod requirement)
    minimal_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"minimal.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "ES",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "rooms": json.dumps([{
            "roomTypeID": "653498",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "adults": 2,
            "children": 0
        }])
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # Test 1: Minimal data without paymentMethod
            print(f"\n📡 Test 1: Minimal data (should require paymentMethod)...")
            response = await client.post(f"{API_BASE_URL}/postReservation", data=minimal_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Message: {data.get('message')}")
                
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ Minimal data works")
                    
                    # Test 2: Add paymentMethod with minimal data
                    print(f"\n📡 Test 2: Minimal data + paymentMethod='noPayment'...")
                    test_data = minimal_data.copy()
                    test_data["paymentMethod"] = "noPayment"
                    test_data["guestEmail"] = f"minimal.payment.{int(asyncio.get_event_loop().time())}@example.com"
                    
                    response2 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                    if response2.status_code == 200:
                        data2 = response2.json()
                        print(f"   Message: {data2.get('message')}")
                        
                        if data2.get('success'):
                            print(f"   🎉 SUCCESS with minimal + paymentMethod!")
                            return True
                        elif "Invalid Parameter Format" in data2.get('message', ''):
                            print(f"   ❌ Still Invalid Parameter Format with minimal data")
                            
                            # Test 3: Try without rooms JSON
                            print(f"\n📡 Test 3: Without rooms JSON...")
                            test_data3 = minimal_data.copy()
                            del test_data3["rooms"]
                            test_data3["paymentMethod"] = "noPayment"
                            test_data3["guestEmail"] = f"no.rooms.{int(asyncio.get_event_loop().time())}@example.com"
                            
                            response3 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data3, headers=headers)
                            if response3.status_code == 200:
                                data3 = response3.json()
                                print(f"   Message: {data3.get('message')}")
                                
                                if "rooms is required" in data3.get('message', ''):
                                    print(f"   ✅ Without rooms: gets to rooms requirement")
                                elif "Invalid Parameter Format" not in data3.get('message', ''):
                                    print(f"   ✅ Without rooms: different error (rooms JSON might be the issue)")
                            
                            # Test 4: Try with simpler rooms JSON
                            print(f"\n📡 Test 4: With simpler rooms JSON...")
                            test_data4 = minimal_data.copy()
                            test_data4["rooms"] = json.dumps([{"roomTypeID": "653498"}])  # Simpler
                            test_data4["paymentMethod"] = "noPayment"
                            test_data4["guestEmail"] = f"simple.rooms.{int(asyncio.get_event_loop().time())}@example.com"
                            
                            response4 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data4, headers=headers)
                            if response4.status_code == 200:
                                data4 = response4.json()
                                print(f"   Message: {data4.get('message')}")
                                
                                if data4.get('success'):
                                    print(f"   🎉 SUCCESS with simpler rooms!")
                                    return True
                                elif "Invalid Parameter Format" not in data4.get('message', ''):
                                    print(f"   ✅ Simpler rooms: different error")
                            
                            # Test 5: Try without any optional parameters
                            print(f"\n📡 Test 5: Absolute minimal (no rooms, no optional params)...")
                            absolute_minimal = {
                                "propertyID": CLOUDBEDS_PROPERTY_ID,
                                "guestFirstName": "John",
                                "guestLastName": "Doe",
                                "guestEmail": f"absolute.minimal.{int(asyncio.get_event_loop().time())}@example.com",
                                "guestCountry": "ES",
                                "startDate": "2025-05-26",
                                "endDate": "2025-05-27",
                                "roomTypeID": "653498",
                                "paymentMethod": "noPayment"
                            }
                            
                            response5 = await client.post(f"{API_BASE_URL}/postReservation", data=absolute_minimal, headers=headers)
                            if response5.status_code == 200:
                                data5 = response5.json()
                                print(f"   Message: {data5.get('message')}")
                                
                                if data5.get('success'):
                                    print(f"   🎉 SUCCESS with absolute minimal!")
                                    return True
                                elif "Invalid Parameter Format" not in data5.get('message', ''):
                                    print(f"   ✅ Absolute minimal: different error")
                                    print(f"   This suggests the issue is with one of the removed parameters")
                        else:
                            print(f"   📝 Different error: {data2.get('message')}")
                else:
                    print(f"   ❌ Unexpected minimal response: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_parameter_elimination())
    if success:
        print(f"\n🎉 FOUND THE WORKING COMBINATION!")
    else:
        print(f"\n🔧 NEED TO CONTINUE PARAMETER ELIMINATION")
