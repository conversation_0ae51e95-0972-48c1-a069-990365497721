"""
Get available payment methods from the API.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def get_payment_methods():
    """Get available payment methods from the API."""
    print("💳 Getting Available Payment Methods...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/json"
    }
    
    # Try different endpoints that might return payment methods
    endpoints_to_try = [
        "getPaymentMethods",
        "getPaymentTypes", 
        "getPaymentOptions",
        "getPaymentCapabilities",
        "getSettings",
        "getPropertySettings",
        "getHotels"  # Sometimes payment methods are in hotel settings
    ]
    
    for endpoint in endpoints_to_try:
        print(f"\n📡 Trying endpoint: {endpoint}")
        try:
            params = {"propertyID": CLOUDBEDS_PROPERTY_ID}
            
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{API_BASE_URL}/{endpoint}", params=params, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    
                    if data.get('success'):
                        print(f"   ✅ Endpoint {endpoint} works!")
                        print(f"   Data keys: {list(data.keys())}")
                        
                        # Look for payment-related data
                        if 'data' in data:
                            if isinstance(data['data'], list) and data['data']:
                                print(f"   First item keys: {list(data['data'][0].keys()) if data['data'][0] else 'Empty'}")
                            elif isinstance(data['data'], dict):
                                print(f"   Data dict keys: {list(data['data'].keys())}")
                        
                        # Print full response if it looks payment-related
                        if any(word in endpoint.lower() for word in ['payment', 'method', 'type']):
                            print(f"   Full response: {json.dumps(data, indent=2)}")
                    else:
                        print(f"   ❌ Error: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    if response.status_code == 404:
                        print(f"   (Endpoint doesn't exist)")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    # Also try to create a reservation without paymentMethod to see what error we get
    print(f"\n📡 Testing reservation creation without paymentMethod:")
    try:
        test_data = {
            "propertyID": "317353",
            "guestFirstName": "Test",
            "guestLastName": "User",
            "guestEmail": f"nopayment.{int(asyncio.get_event_loop().time())}@example.com",
            "guestCountry": "FR",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498",
            "adults": "2",
            "children": "0",
            "rooms": json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])
            # No paymentMethod
        }
        
        headers_form = {
            "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers_form)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! No payment method needed!")
                    return True
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    asyncio.run(get_payment_methods())
