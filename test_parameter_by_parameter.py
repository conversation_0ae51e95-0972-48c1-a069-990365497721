"""
Test parameters one by one to find the problematic one.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_parameter_by_parameter():
    """Test parameters one by one."""
    print("🔍 Testing Parameters One by One...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base working parameters (we know these work because we get "Parameter paymentMethod is required")
    base_data = {
        "propertyID": "317353",
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": f"base.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": "653498"
    }
    
    # Parameters to test one by one
    additional_params = [
        ("paymentMethod", "Cash"),
        ("sourceID", "s-1"),
        ("guestPhone", "+1234567890"),
        ("rooms as JSON", json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])),
        ("thirdPartyIdentifier", "test-123"),
        ("notes", "Test reservation"),
        ("customRate", "250.00"),
        ("rateID", "1")
    ]
    
    print(f"\n📡 Base test (should give 'Parameter paymentMethod is required'):")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=base_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test each additional parameter
    for param_name, param_value in additional_params:
        print(f"\n📡 Testing with {param_name}: {param_value}")
        try:
            test_data = base_data.copy()
            test_data["guestEmail"] = f"test.{param_name.replace(' ', '')}.{int(asyncio.get_event_loop().time())}@example.com"
            
            if param_name == "rooms as JSON":
                test_data["rooms"] = param_value
            else:
                test_data[param_name] = param_value
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Parameter '{param_name}' completed the reservation!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        return param_name
                    elif "Invalid Parameter Format" in data.get('message', ''):
                        print(f"   ❌ Parameter '{param_name}' causes 'Invalid Parameter Format'")
                    else:
                        print(f"   ✅ Parameter '{param_name}' accepted, next error: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    return None

if __name__ == "__main__":
    asyncio.run(test_parameter_by_parameter())
