"""
Test without propertyID in the JSON body since it's in the header.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_without_property_in_body():
    """Test without propertyID in JSON body."""
    print("🎯 Testing Without PropertyID in JSON Body...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/json"
    }
    
    # Structure WITHOUT propertyID in body (since it's in header)
    reservation_data = {
        "guestData": {
            "firstName": "<PERSON>",
            "lastName": "Doe",
            "email": f"no.property.{int(asyncio.get_event_loop().time())}@example.com",
            "phone": "+34123456789",
            "address": "Calle Mayor 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001"
        },
        "roomsData": [
            {
                "roomTypeID": "653498",
                "startDate": "2025-05-26",
                "endDate": "2025-05-27",
                "adults": 2,
                "children": 0,
                "roomName": "Garden Deluxe"
            }
        ],
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-no-prop-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": True,
        "sourceID": "s-2-1"
    }
    
    print(f"\n📋 Structure WITHOUT propertyID in body:")
    print(json.dumps(reservation_data, indent=2))
    print(f"\n📋 Headers:")
    print(json.dumps(headers, indent=2))
    
    try:
        print(f"\n🚀 Testing without propertyID in body...")
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", json=reservation_data, headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ PERFECT! Back to payment method requirement")
                    
                    # Now try with payment method
                    print(f"\n🔄 Adding payment method...")
                    test_data = reservation_data.copy()
                    test_data["paymentMethod"] = "credit"
                    
                    response2 = await client.post(f"{API_BASE_URL}/postReservation", json=test_data, headers=headers)
                    if response2.status_code == 200:
                        data2 = response2.json()
                        print(f"      Success: {data2.get('success')}")
                        print(f"      Message: {data2.get('message')}")
                        
                        if data2.get('success'):
                            print(f"      🎉 SUCCESS! Reservation created!")
                            print(f"      Reservation ID: {data2.get('reservationID')}")
                            print(f"      Confirmation Code: {data2.get('confirmationCode')}")
                            return True
                        else:
                            print(f"      ❌ Payment method issue: {data2.get('message')}")
                
                elif data.get('success'):
                    print(f"   🎉 SUCCESS! No payment method needed!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    return True
                else:
                    print(f"   📝 Different error: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_without_property_in_body())
    if success:
        print(f"\n🎉 RESERVATION CREATION SUCCESSFUL!")
    else:
        print(f"\n🔧 STILL WORKING ON THE SOLUTION")
