"""
Test reservation creation without payment method.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_without_payment():
    """Test reservation creation without payment method."""
    print("🎯 Testing Reservation Creation Without Payment Method...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Complete reservation data without payment method
    reservation_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"success.test.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "guestPhone": "+34123456789",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": 2,
        "children": 0,
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-success-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": "true",
        "sourceID": "s-2-1"
    }
    
    # Add rooms data
    rooms = [{
        "roomTypeID": "653498",
        "adults": 2,
        "children": 0,
        "startDate": "2025-05-26",
        "endDate": "2025-05-27"
    }]
    reservation_data["rooms"] = json.dumps(rooms)
    
    print(f"\n📋 Reservation data (without payment method):")
    for key, value in reservation_data.items():
        if key == "rooms":
            print(f"   {key}: {value}")
        else:
            print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 Creating reservation without payment method...")
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=reservation_data, headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created without payment method!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    print(f"   Confirmation Code: {data.get('confirmationCode')}")
                    print(f"   Full response: {json.dumps(data, indent=2)}")
                    
                    # Verify the reservation
                    if data.get('reservationID'):
                        print(f"\n🔍 Verifying reservation...")
                        verify_headers = {
                            "x-api-key": CLOUDBEDS_API_KEY,
                            "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
                            "Content-Type": "application/json"
                        }
                        verify_params = {
                            "reservationID": data['reservationID'],
                            "propertyID": CLOUDBEDS_PROPERTY_ID
                        }
                        verify_response = await client.get(f"{API_BASE_URL}/getReservation", params=verify_params, headers=verify_headers)
                        if verify_response.status_code == 200:
                            verify_data = verify_response.json()
                            if verify_data.get('success') is not False:
                                print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                            else:
                                print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                    
                    return True
                else:
                    print(f"\n❌ Failed: {data.get('message')}")
                    if "paymentMethod is required" in data.get('message', ''):
                        print(f"   💡 Payment method is definitely required by this API")
                    elif "Invalid Parameter Format" in data.get('message', ''):
                        print(f"   🔍 Still getting Invalid Parameter Format - check other parameters")
                    else:
                        print(f"   📝 Different error - progress made!")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_without_payment())
    if success:
        print(f"\n🎉 RESERVATION CREATION SUCCESSFUL!")
        print(f"💡 Payment method is NOT required for reservation creation")
    else:
        print(f"\n🔧 STILL WORKING ON THE SOLUTION")
