"""
Test different rooms parameter formats.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_rooms_formats():
    """Test different rooms parameter formats."""
    print("🏨 Testing Different Rooms Parameter Formats...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498"
    }
    
    # Test 1: JSON string format
    print("\n📡 Test 1: Rooms as JSON string")
    try:
        rooms_json = json.dumps([{
            "roomTypeID": "653498",
            "adults": "2",
            "children": "0"
        }])
        
        test_data = base_data.copy()
        test_data["rooms"] = rooms_json
        
        print(f"   Rooms value: {rooms_json}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reservation created!")
                    return
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 2: Form array format
    print("\n📡 Test 2: Rooms as form array")
    try:
        test_data = base_data.copy()
        test_data["rooms[0][roomTypeID]"] = "653498"
        test_data["rooms[0][adults]"] = "2"
        test_data["rooms[0][children]"] = "0"
        
        print(f"   Using form array format")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reservation created!")
                    return
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 3: Simple string format
    print("\n📡 Test 3: Rooms as simple string")
    try:
        test_data = base_data.copy()
        test_data["rooms"] = "653498"  # Just the room type ID
        
        print(f"   Rooms value: 653498")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reservation created!")
                    return
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 4: Without rooms parameter but with adults/children
    print("\n📡 Test 4: Without rooms parameter, using adults/children directly")
    try:
        test_data = base_data.copy()
        test_data["adults"] = "2"
        test_data["children"] = "0"
        
        print(f"   Using adults/children directly")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reservation created!")
                    return
    except Exception as e:
        print(f"   💥 Exception: {e}")

if __name__ == "__main__":
    asyncio.run(test_rooms_formats())
