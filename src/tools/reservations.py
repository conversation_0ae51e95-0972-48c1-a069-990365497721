"""
Reservation tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds reservations.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_reservations(
    days_back: int = 30,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation data
    """
    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    return await api_request("getReservations", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_details(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed reservation data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getReservation", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_invoice(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Invoice data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getInvoice", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_reservation(
    reservation_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (dict): Reservation data containing:
            - Guest information: firstName, lastName, email, phone (optional), guestCountry (optional, defaults to 'US')
            - Stay details: startDate, endDate (YYYY-MM-DD format)
            - Room details: roomTypeID or roomID
            - Occupancy: adults, children (optional, defaults to 1 adult)
            - Rate: rateID (optional), customRate (optional)
            - Payment: paymentMethod (optional, defaults to 'cash')
            - Source: sourceID (optional, defaults to direct booking)
            - Notes: notes (optional)
            - Third party: thirdPartyIdentifier (optional)
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created reservation data or error information
    """
    # Validate required fields
    required_fields = ['firstName', 'lastName', 'email', 'startDate', 'endDate']
    missing_fields = [field for field in required_fields if not reservation_data.get(field)]

    if missing_fields:
        return {
            "success": False,
            "message": f"Missing required fields: {', '.join(missing_fields)}"
        }

    # Validate date format
    try:
        start_date = datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')

        if end_date <= start_date:
            return {
                "success": False,
                "message": "End date must be after start date"
            }
    except ValueError as e:
        return {
            "success": False,
            "message": f"Invalid date format. Use YYYY-MM-DD: {str(e)}"
        }

    # Validate room assignment - must have either roomTypeID or roomID
    if not reservation_data.get('roomTypeID') and not reservation_data.get('roomID'):
        return {
            "success": False,
            "message": "Either roomTypeID or roomID must be specified"
        }

    # Prepare the request data for Cloudbeds API
    # Based on extensive testing, use the verified working structure
    api_data = {
        # Property ID - required
        "propertyID": property_id or CLOUDBEDS_PROPERTY_ID,

        # Guest information - exact field names verified through testing
        "guestFirstName": reservation_data['firstName'],
        "guestLastName": reservation_data['lastName'],
        "guestEmail": reservation_data['email'],
        "guestCountry": reservation_data.get('guestCountry', 'FR'),  # Use FR as default (tested working)

        # Stay dates
        "startDate": reservation_data['startDate'],
        "endDate": reservation_data['endDate'],

        # Room assignment - verified working
        "roomTypeID": str(reservation_data['roomTypeID']) if reservation_data.get('roomTypeID') else str(reservation_data['roomID']),

        # Occupancy - required at top level
        "adults": str(reservation_data.get('adults', 1)),
        "children": str(reservation_data.get('children', 0)),
    }

    # Add optional guest information
    if reservation_data.get('phone'):
        api_data['guestPhone'] = reservation_data['phone']

    # Room assignment - rooms array in JSON format (verified required)
    rooms = [{
        "roomTypeID": str(reservation_data['roomTypeID']) if reservation_data.get('roomTypeID') else None,
        "adults": str(reservation_data.get('adults', 1)),
        "children": str(reservation_data.get('children', 0))
    }]

    # Remove None values from room object
    rooms[0] = {k: v for k, v in rooms[0].items() if v is not None}

    # Add specific room ID if provided
    if reservation_data.get('roomID'):
        rooms[0]['roomID'] = str(reservation_data['roomID'])

    # Add rate information to room
    if reservation_data.get('rateID'):
        rooms[0]['rateID'] = str(reservation_data['rateID'])
    if reservation_data.get('customRate'):
        rooms[0]['customRate'] = str(reservation_data['customRate'])

    # Convert rooms to JSON string for form data
    import json
    api_data['rooms'] = json.dumps(rooms)

    # Source information (optional)
    if reservation_data.get('sourceID'):
        api_data['sourceID'] = str(reservation_data['sourceID'])

    # Additional information
    if reservation_data.get('thirdPartyIdentifier'):
        api_data['thirdPartyIdentifier'] = str(reservation_data['thirdPartyIdentifier'])
    if reservation_data.get('notes'):
        api_data['notes'] = str(reservation_data['notes'])

    # Payment method - this is currently causing "Invalid Parameter Format"
    # The API requires paymentMethod but none of the tested values work
    # For now, we'll return an error explaining this limitation
    if reservation_data.get('paymentMethod'):
        return {
            "success": False,
            "message": "Payment method parameter causes 'Invalid Parameter Format' error. " +
                      "The Cloudbeds API requires paymentMethod but the correct format is unknown. " +
                      "Available payment methods from API: credit, cards, visa, master - but none work. " +
                      "This may require additional API documentation or support from Cloudbeds."
        }

    # Make the API request
    try:
        result = await api_request(
            "postReservation",
            data=api_data,
            property_id=property_id or CLOUDBEDS_PROPERTY_ID,
            method="POST"
        )

        # Check if the request was successful
        if isinstance(result, dict) and result.get('success') is False:
            return result

        return result

    except Exception as e:
        return {
            "success": False,
            "message": f"Error creating reservation: {str(e)}"
        }

async def update_reservation(
    reservation_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (dict): Data to update
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data
    """
    # This would need to be implemented with a PUT request
    # For now, we'll return a placeholder
    return {"message": "Reservation update not implemented yet"}
