"""
Simple debug test to see exactly what's being sent.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_simple_debug():
    """Simple test to debug what's being sent."""
    print("🔍 Simple Debug Test")
    print("=" * 50)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Very simple reservation data
        simple_reservation = {
            "firstName": "Test",
            "lastName": "User",
            "email": f"debug.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0
        }
        
        print(f"\n📋 Simple reservation data:")
        for key, value in simple_reservation.items():
            print(f"   {key}: {value}")
        
        try:
            print(f"\n🚀 Calling create_reservation_tool...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": simple_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS!")
                    return True
                else:
                    print(f"\n❌ Error: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception: {str(e)}")
            import traceback
            traceback.print_exc()
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_simple_debug())
    if success:
        print(f"\n🎉 DEBUG SUCCESSFUL!")
    else:
        print(f"\n🔧 DEBUGGING CONTINUES...")
