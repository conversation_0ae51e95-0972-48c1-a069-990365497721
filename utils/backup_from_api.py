#!/usr/bin/env python3
"""
Cloudbeds API Data Backup Utility

This script retrieves data from the Cloudbeds API and saves it to CSV files.
Useful for data backup and analysis purposes.

Usage:
    python backup_from_api.py [--days DAYS] [--output-dir OUTPUT_DIR]

Options:
    --days DAYS           Number of days to look back for reservations (default: 30)
    --output-dir OUTPUT_DIR  Output directory for CSV files (default: ./cloudbeds_backup_YYYY-MM-DD)
"""

import os
import sys
import json
import logging
import argparse
import datetime
import time
from pathlib import Path

import requests
import pandas as pd
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('cloudbeds_backup.log')
    ]
)
logger = logging.getLogger('cloudbeds_backup')

# API rate limit (requests per second)
API_RATE_LIMIT = 5
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Retrieve comprehensive data from Cloudbeds API and save to CSV files')
    parser.add_argument('--days', type=int, default=30, help='Number of days to look back for reservations')
    parser.add_argument('--output-dir', type=str, help='Output directory for CSV files')
    parser.add_argument('--property-id', type=str, help='Cloudbeds property ID')
    return parser.parse_args()

def load_environment_variables():
    """Load environment variables from .env file."""
    load_dotenv()
    api_key = os.getenv('CLOUDBEDS_API_KEY')
    property_id = os.getenv('CLOUDBEDS_PROPERTY_ID')

    if not api_key:
        logger.error("CLOUDBEDS_API_KEY not found in environment variables")
        sys.exit(1)

    return {
        'api_key': api_key,
        'property_id': property_id
    }

def get_access_token(api_key):
    """
    Get access token from Cloudbeds API using API key.

    Args:
        api_key (str): Cloudbeds API key

    Returns:
        str: Access token
    """
    logger.info("Using API key directly as access token")

    # For the Cloudbeds API, we can use the API key directly as the access token
    # This is based on the API documentation and the format of the API key
    return api_key

def api_request(endpoint, access_token, params=None, property_id=None):
    """
    Make a request to the Cloudbeds API with rate limiting.

    Args:
        endpoint (str): API endpoint
        access_token (str): Cloudbeds API access token
        params (dict, optional): Request parameters
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: API response data
    """
    url = f"{API_BASE_URL}/{endpoint}"

    # Initialize parameters if not provided
    if params is None:
        params = {}

    # Add property ID if provided
    if property_id:
        params["propertyID"] = property_id

    # Request headers
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }

    try:
        # Make API request
        response = requests.get(url, params=params, headers=headers)

        # Respect rate limit
        time.sleep(1.0 / API_RATE_LIMIT)

        # Check if request was successful
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                return data.get('data', [])
            else:
                logger.error(f"API returned error for {endpoint}: {data.get('message')}")
                return []
        else:
            logger.error(f"API request failed for {endpoint} with status code {response.status_code}: {response.text}")
            return []
    except Exception as e:
        logger.error(f"Error making API request to {endpoint}: {str(e)}")
        return []

def get_reservations(access_token, property_id=None, days_back=30):
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        access_token (str): Cloudbeds API access token
        property_id (str, optional): Cloudbeds property ID
        days_back (int): Number of days to look back for reservations

    Returns:
        list: List of reservation data
    """
    logger.info(f"Retrieving reservations for the past {days_back} days")

    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    reservations = api_request("getReservations", access_token, params, property_id)
    logger.info(f"Successfully retrieved {len(reservations)} reservations")
    return reservations

def get_reservations_with_rate_details(access_token, property_id=None, days_back=30):
    """
    Retrieve reservations with rate details from Cloudbeds API.

    Args:
        access_token (str): Cloudbeds API access token
        property_id (str, optional): Cloudbeds property ID
        days_back (int): Number of days to look back for reservations

    Returns:
        list: List of reservation data with rate details
    """
    logger.info(f"Retrieving reservations with rate details for the past {days_back} days")

    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str
    }

    reservations = api_request("getReservationsWithRateDetails", access_token, params, property_id)
    logger.info(f"Successfully retrieved {len(reservations)} reservations with rate details")
    return reservations

def get_reservation_details(access_token, reservation_ids, property_id=None):
    """
    Retrieve detailed information for specific reservations.

    Args:
        access_token (str): Cloudbeds API access token
        reservation_ids (list): List of reservation IDs
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of detailed reservation data
    """
    logger.info(f"Retrieving detailed information for {len(reservation_ids)} reservations")

    reservation_details = []

    for reservation_id in reservation_ids:
        logger.info(f"Retrieving details for reservation {reservation_id}")

        # Request parameters
        params = {
            "reservationID": reservation_id
        }

        details = api_request("getReservation", access_token, params, property_id)
        if details:
            reservation_details.append(details)

    logger.info(f"Successfully retrieved details for {len(reservation_details)} reservations")
    return reservation_details

def get_reservation_invoice_information(access_token, reservation_ids, property_id=None):
    """
    Retrieve invoice information for specific reservations.

    Args:
        access_token (str): Cloudbeds API access token
        reservation_ids (list): List of reservation IDs
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation invoice data
    """
    logger.info(f"Retrieving invoice information for {len(reservation_ids)} reservations")

    invoice_information = []

    for reservation_id in reservation_ids:
        logger.info(f"Retrieving invoice information for reservation {reservation_id}")

        # Request parameters
        params = {
            "reservationID": reservation_id
        }

        invoice = api_request("getReservationInvoiceInformation", access_token, params, property_id)
        if invoice:
            invoice_information.append({
                "reservationID": reservation_id,
                "invoice": invoice
            })

    logger.info(f"Successfully retrieved invoice information for {len(invoice_information)} reservations")
    return invoice_information

def get_reservation_assignments(access_token, property_id=None, days_back=30):
    """
    Retrieve reservation assignments from Cloudbeds API.

    Args:
        access_token (str): Cloudbeds API access token
        property_id (str, optional): Cloudbeds property ID
        days_back (int): Number of days to look back for reservations

    Returns:
        list: List of reservation assignment data
    """
    logger.info(f"Retrieving reservation assignments for the past {days_back} days")

    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=7)  # Include a week of future assignments
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    assignments = []
    current_date = start_date

    while current_date <= end_date:
        date_str = current_date.strftime('%Y-%m-%d')
        logger.info(f"Retrieving reservation assignments for date {date_str}")

        # Request parameters
        params = {
            "date": date_str
        }

        daily_assignments = api_request("getReservationAssignments", access_token, params, property_id)
        if daily_assignments:
            for assignment in daily_assignments:
                assignment["date"] = date_str
                assignments.append(assignment)

        current_date += datetime.timedelta(days=1)

    logger.info(f"Successfully retrieved {len(assignments)} reservation assignments")
    return assignments

def get_room_types(access_token, property_id=None):
    """
    Retrieve room types from Cloudbeds API.

    Args:
        access_token (str): Cloudbeds API access token
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of room type data
    """
    logger.info("Retrieving room types")

    room_types = api_request("getRoomTypes", access_token, property_id=property_id)
    logger.info(f"Successfully retrieved {len(room_types)} room types")
    return room_types

def get_rooms(access_token, property_id=None):
    """
    Retrieve rooms from Cloudbeds API.

    Args:
        access_token (str): Cloudbeds API access token
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of room data
    """
    logger.info("Retrieving rooms")

    rooms = api_request("getRooms", access_token, property_id=property_id)
    logger.info(f"Successfully retrieved {len(rooms)} rooms")
    return rooms

def get_guest_details(access_token, guest_ids, property_id=None):
    """
    Retrieve detailed information for specific guests.

    Args:
        access_token (str): Cloudbeds API access token
        guest_ids (list): List of guest IDs
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of detailed guest data
    """
    logger.info(f"Retrieving detailed information for {len(guest_ids)} guests")

    guest_details = []

    for guest_id in guest_ids:
        logger.info(f"Retrieving details for guest {guest_id}")

        # Request parameters
        params = {
            "guestID": guest_id
        }

        details = api_request("getGuest", access_token, params, property_id)
        if details:
            guest_details.append(details)

    logger.info(f"Successfully retrieved details for {len(guest_details)} guests")
    return guest_details

def process_data_to_dataframe(data, flatten=True):
    """
    Process data and convert to DataFrame.

    Args:
        data (list): List of data from API
        flatten (bool): Whether to flatten nested JSON

    Returns:
        pandas.DataFrame: Processed data
    """
    if not data:
        return pd.DataFrame()

    try:
        if flatten:
            df = pd.json_normalize(data)
        else:
            df = pd.DataFrame(data)
        return df
    except Exception as e:
        logger.error(f"Error processing data to DataFrame: {str(e)}")
        return pd.DataFrame()

def save_to_csv(df, filename, output_dir):
    """
    Save DataFrame to CSV file.

    Args:
        df (pandas.DataFrame): DataFrame to save
        filename (str): Output filename
        output_dir (str): Output directory

    Returns:
        str: Path to saved CSV file
    """
    if df.empty:
        logger.warning(f"No data to save for {filename}")
        return None

    try:
        # Create output directory if it doesn't exist
        output_path = Path(output_dir) / filename
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # Save to CSV
        df.to_csv(output_path, index=False)
        logger.info(f"Data saved to {output_path}")
        return str(output_path)
    except Exception as e:
        logger.error(f"Error saving data to CSV {filename}: {str(e)}")
        return None

def main():
    """Main function to run the script."""
    logger.info("Starting Cloudbeds comprehensive data backup")

    # Parse command line arguments
    args = parse_arguments()

    # Load environment variables
    env_vars = load_environment_variables()
    api_key = env_vars['api_key']
    property_id = args.property_id or env_vars['property_id']

    # Get access token
    access_token = get_access_token(api_key)
    if not access_token:
        logger.error("Failed to get access token. Exiting.")
        sys.exit(1)

    # Create output directory
    if args.output_dir:
        output_dir = args.output_dir
    else:
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        output_dir = f"cloudbeds_backup_{today}"

    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    logger.info(f"Output directory: {output_dir}")

    # Get basic reservations
    reservations = get_reservations(access_token, property_id, args.days)
    reservations_df = process_data_to_dataframe(reservations)
    save_to_csv(reservations_df, "reservations.csv", output_dir)

    # Get reservations with rate details
    reservations_with_rates = get_reservations_with_rate_details(access_token, property_id, args.days)
    reservations_with_rates_df = process_data_to_dataframe(reservations_with_rates)
    save_to_csv(reservations_with_rates_df, "reservations_with_rates.csv", output_dir)

    # Get reservation assignments
    assignments = get_reservation_assignments(access_token, property_id, args.days)
    assignments_df = process_data_to_dataframe(assignments)
    save_to_csv(assignments_df, "reservation_assignments.csv", output_dir)

    # Get room types
    room_types = get_room_types(access_token, property_id)
    room_types_df = process_data_to_dataframe(room_types)
    save_to_csv(room_types_df, "room_types.csv", output_dir)

    # Get rooms
    rooms = get_rooms(access_token, property_id)
    rooms_df = process_data_to_dataframe(rooms)
    save_to_csv(rooms_df, "rooms.csv", output_dir)

    # Get detailed information for each reservation
    if reservations:
        reservation_ids = [r.get('reservationID') for r in reservations if r.get('reservationID')]

        # Limit to first 20 reservations to avoid rate limiting issues
        reservation_ids_sample = reservation_ids[:20] if len(reservation_ids) > 20 else reservation_ids

        # Get detailed reservation information
        reservation_details = get_reservation_details(access_token, reservation_ids_sample, property_id)
        reservation_details_df = process_data_to_dataframe(reservation_details)
        save_to_csv(reservation_details_df, "reservation_details.csv", output_dir)

        # Get invoice information
        invoice_information = get_reservation_invoice_information(access_token, reservation_ids_sample, property_id)

        # Save raw invoice data as JSON
        invoice_path = Path(output_dir) / "invoice_information.json"
        with open(invoice_path, 'w') as f:
            json.dump(invoice_information, f, indent=2)
        logger.info(f"Invoice information saved to {invoice_path}")

        # Extract guest IDs
        guest_ids = []
        for reservation in reservations:
            guest_id = reservation.get('guestID')
            if guest_id and guest_id not in guest_ids:
                guest_ids.append(guest_id)

        # Limit to first 20 guests to avoid rate limiting issues
        guest_ids_sample = guest_ids[:20] if len(guest_ids) > 20 else guest_ids

        # Get guest details
        guest_details = get_guest_details(access_token, guest_ids_sample, property_id)
        guest_details_df = process_data_to_dataframe(guest_details)
        save_to_csv(guest_details_df, "guest_details.csv", output_dir)

    logger.info(f"Backup completed successfully. Data saved to {output_dir}")

if __name__ == "__main__":
    main()
