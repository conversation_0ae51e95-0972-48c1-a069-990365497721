# Cloudbeds MCP Tools Reference Guide

## Quick Start

Connect to the MCP server:
```python
from fastmcp import Client

async def main():
    async with <PERSON><PERSON>("http://localhost:8000/mcp") as client:
        # Use tools here
        pass
```

## 🔧 Available Tools

### Room Management

#### `get_room_types_tool()`
Get all room types configured in the property.
```python
room_types = await client.call_tool("get_room_types_tool")
# Returns: List of room types with details, capacity, features
```

#### `get_rooms_tool()`
Get all individual rooms in the property.
```python
rooms = await client.call_tool("get_rooms_tool")
# Returns: List of all physical rooms with IDs and assignments
```

#### `get_availability_tool(start_date, end_date)`
Check room availability for a date range.
```python
availability = await client.call_tool("get_availability_tool", {
    "start_date": "2025-06-01",
    "end_date": "2025-06-07"
})
# Returns: Availability data for the specified period
```

### Reservation Management

#### `get_reservations_tool(days_back=30)`
Get reservations within a specified time period.
```python
reservations = await client.call_tool("get_reservations_tool", {
    "days_back": 7  # Last 7 days
})
# Returns: List of reservations with guest info, dates, status
```

#### `get_reservation_tool(reservation_id)`
Get detailed information for a specific reservation.
```python
details = await client.call_tool("get_reservation_tool", {
    "reservation_id": "5113613993769"
})
# Returns: Complete reservation details
```

#### `get_invoice_tool(reservation_id)`
Get invoice/billing information for a reservation.
```python
invoice = await client.call_tool("get_invoice_tool", {
    "reservation_id": "5113613993769"
})
# Returns: Invoice and billing details
```

#### `create_reservation_tool(reservation_data)` ⚠️
Create a new reservation (placeholder implementation).
```python
result = await client.call_tool("create_reservation_tool", {
    "reservation_data": {"guest": "John Doe", "dates": "..."}
})
# Returns: Placeholder message (not implemented)
```

### Guest Management

#### `search_guests_tool(search_term)`
Search for guests by name, email, or phone.
```python
guests = await client.call_tool("search_guests_tool", {
    "search_term": "<EMAIL>"
})
# Returns: List of matching guests
```

#### `get_guest_tool(guest_id)`
Get detailed information for a specific guest.
```python
guest = await client.call_tool("get_guest_tool", {
    "guest_id": "139240972"
})
# Returns: Complete guest profile
```

## 📚 Available Resources

### Static Resources

#### Recent Reservations
```python
recent = await client.read_resource("cloudbeds://reservations/recent")
# Returns: Last 30 days of reservations
```

#### Room Types
```python
types = await client.read_resource("cloudbeds://rooms/types")
# Returns: All room type configurations
```

#### All Rooms
```python
rooms = await client.read_resource("cloudbeds://rooms")
# Returns: All individual room data
```

#### Availability (Next 30 Days)
```python
availability = await client.read_resource("cloudbeds://availability/next30days")
# Returns: 30-day availability forecast
```

#### Property Information
```python
property_info = await client.read_resource("cloudbeds://property")
# Returns: Property configuration and details
```

### Dynamic Resources (Templates)

#### Specific Reservation
```python
reservation = await client.read_resource("cloudbeds://reservations/12345")
# Returns: Details for reservation ID 12345
```

## 💬 Available Prompts

### Reservation Summary
Generate a prompt to summarize reservation data.
```python
summary_prompt = await client.get_prompt("summarize_reservation", {
    "reservation_data": json.dumps({
        "reservationID": "12345",
        "guestName": "John Doe",
        "startDate": "2025-06-01",
        "endDate": "2025-06-03",
        "roomName": "Deluxe King",
        "status": "confirmed"
    })
})
# Returns: Formatted prompt for LLM to summarize reservation
```

### New Reservation
Generate a prompt for creating a new reservation.
```python
new_res_prompt = await client.get_prompt("new_reservation_prompt")
# Returns: Prompt template for gathering reservation information
```

## 📋 Data Formats

### Dates
All dates use ISO format: `YYYY-MM-DD`
```python
"start_date": "2025-06-01"
"end_date": "2025-06-07"
```

### Response Format
All tool responses return a list of content objects:
```python
result = await client.call_tool("get_room_types_tool")
data = json.loads(result[0].text)  # Parse JSON from text content
```

### Common Fields

#### Reservation Object
```json
{
    "reservationID": "5113613993769",
    "guestName": "John Doe",
    "startDate": "2025-05-22",
    "endDate": "2025-05-23",
    "status": "confirmed",
    "balance": 500,
    "adults": "2",
    "children": "0"
}
```

#### Room Type Object
```json
{
    "roomTypeID": "653496",
    "roomTypeName": "Junior Suite Sea View",
    "roomTypeNameShort": "JVM",
    "maxGuests": 3,
    "roomTypeUnits": 6
}
```

#### Room Object
```json
{
    "roomID": "653496-0",
    "roomName": "JVM(1)",
    "maxGuests": 3,
    "roomTypeID": "653496",
    "roomTypeName": "Junior Suite Sea View"
}
```

## 🚨 Error Handling

### Common Patterns
```python
try:
    result = await client.call_tool("get_reservation_tool", {
        "reservation_id": "invalid_id"
    })
    if result and result[0].text:
        data = json.loads(result[0].text)
        if not data:
            print("No data returned")
    else:
        print("Empty response")
except Exception as e:
    print(f"Error: {e}")
```

### Status Codes
- **Success**: Tool returns data (may be empty list/object)
- **Invalid ID**: Returns empty list/object
- **API Error**: Exception raised with error message

## 💡 Best Practices

1. **Always check for empty responses** before processing data
2. **Use try-catch blocks** for error handling
3. **Parse JSON responses** from text content
4. **Validate date formats** before sending to tools
5. **Use appropriate time ranges** for reservation queries
6. **Cache frequently accessed data** like room types

## 🔗 Integration Examples

### Get Today's Arrivals
```python
from datetime import datetime

today = datetime.now().strftime('%Y-%m-%d')
reservations = await client.call_tool("get_reservations_tool", {"days_back": 1})
data = json.loads(reservations[0].text)
arrivals = [r for r in data if r['startDate'] == today]
```

### Check Room Availability
```python
availability = await client.call_tool("get_availability_tool", {
    "start_date": "2025-06-01",
    "end_date": "2025-06-07"
})
```

### Generate Reservation Summary
```python
# Get reservation details
details = await client.call_tool("get_reservation_tool", {"reservation_id": "12345"})
reservation_data = json.loads(details[0].text)

# Generate summary prompt
prompt = await client.get_prompt("summarize_reservation", {
    "reservation_data": json.dumps(reservation_data)
})
```
