{"tools": {"get_room_types_tool": {"success": true, "result": ["type='text' text='[\\n  {\\n    \"roomTypeID\": \"653496\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Junior Suite Sea View\",\\n    \"roomTypeNameShort\": \"JVM\",\\n    \"roomTypeDescription\": \"<div><div>The ocean suite offers a spectacular view of the sea of \\\\n\\u200b\\u200bEssaouira. Enjoy the cool breeze and the sound of the waves in this \\\\nspacious room, elegantly decorated in a modern Moroccan style, thus \\\\nproviding an atmosphere of peace and relaxation.</div></div><br>\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 3,\\n    \"adultsIncluded\": 2,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": {\\n      \"2\": \"Bathrobes\",\\n      \"4\": \"Cable television\",\\n      \"5\": \"Ceiling fan\",\\n      \"8\": \"Coffee maker\",\\n      \"7\": \"Cribs upon request\",\\n      \"6\": \"Hairdryer\",\\n      \"9\": \"Minibar\",\\n      \"1\": \"Safe Box\",\\n      \"3\": \"Slippers\",\\n      \"10\": \"Wireless internet (WiFi)\",\\n      \"0\": \"Work Zone\"\\n    },\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 6\\n  },\\n  {\\n    \"roomTypeID\": \"653497\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Ocean Deluxe\",\\n    \"roomTypeNameShort\": \"OD\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 5,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  },\\n  {\\n    \"roomTypeID\": \"653498\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Garden Junior\",\\n    \"roomTypeNameShort\": \"GJ\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 2,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  },\\n  {\\n    \"roomTypeID\": \"653499\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Ocean Junior\",\\n    \"roomTypeNameShort\": \"OJ\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 2,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  }\\n]' annotations=None"], "error": null, "timestamp": "2025-05-25T11:18:48.327069"}, "get_rooms_tool": {"success": true, "result": ["type='text' text='[\\n  {\\n    \"propertyID\": \"317353\",\\n    \"rooms\": [\\n      {\\n        \"roomID\": \"653496-0\",\\n        \"roomName\": \"JVM(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-1\",\\n        \"roomName\": \"JVM(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-2\",\\n        \"roomName\": \"JVM(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-3\",\\n        \"roomName\": \"JVM(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-4\",\\n        \"roomName\": \"JVM(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-5\",\\n        \"roomName\": \"JVM(6)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653497-0\",\\n        \"roomName\": \"OD(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-1\",\\n        \"roomName\": \"OD(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-2\",\\n        \"roomName\": \"OD(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-3\",\\n        \"roomName\": \"OD(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-4\",\\n        \"roomName\": \"OD(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653498-0\",\\n        \"roomName\": \"GJ(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-1\",\\n        \"roomName\": \"GJ(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-2\",\\n        \"roomName\": \"GJ(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-3\",\\n        \"roomName\": \"GJ(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-4\",\\n        \"roomName\": \"GJ(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-0\",\\n        \"roomName\": \"OJ(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-1\",\\n        \"roomName\": \"OJ(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-2\",\\n        \"roomName\": \"OJ(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-3\",\\n        \"roomName\": \"OJ(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      }\\n    ]\\n  }\\n]' annotations=None"], "error": null, "timestamp": "2025-05-25T11:18:49.560032"}, "get_reservations_tool": {"success": true, "result": ["type='text' text='[\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"5113613993769\",\\n    \"dateCreated\": \"2025-05-22 13:43:41\",\\n    \"dateModified\": \"2025-05-25 02:01:18\",\\n    \"status\": \"no_show\",\\n    \"guestID\": \"139240972\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-05-22\",\\n    \"endDate\": \"2025-05-23\",\\n    \"adults\": \"2\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"server-1747946620100-182\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8345540431982\",\\n    \"dateCreated\": \"2025-05-22 07:55:03\",\\n    \"dateModified\": \"2025-05-22 07:55:07\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213392\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925702566-s4w5l\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"6971110202035\",\\n    \"dateCreated\": \"2025-05-22 07:55:01\",\\n    \"dateModified\": \"2025-05-22 07:55:05\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213382\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925700588-8z4mn\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"1928974196042\",\\n    \"dateCreated\": \"2025-05-22 07:54:59\",\\n    \"dateModified\": \"2025-05-22 07:55:03\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213379\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925698501-ydlbt\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7927359142743\",\\n    \"dateCreated\": \"2025-05-22 07:54:57\",\\n    \"dateModified\": \"2025-05-22 07:55:01\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213378\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925696459-k9o0f\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7345756948254\",\\n    \"dateCreated\": \"2025-05-22 07:54:55\",\\n    \"dateModified\": \"2025-05-22 07:54:59\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213375\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925694505-ytyt3\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4436526719516\",\\n    \"dateCreated\": \"2025-05-22 07:54:53\",\\n    \"dateModified\": \"2025-05-22 07:54:57\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213373\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925692569-uz3pc\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2183375888922\",\\n    \"dateCreated\": \"2025-05-22 07:54:51\",\\n    \"dateModified\": \"2025-05-22 07:54:55\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213370\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925690768-lxj0f\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"1363735446951\",\\n    \"dateCreated\": \"2025-05-22 07:54:49\",\\n    \"dateModified\": \"2025-05-22 07:54:53\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213369\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925688990-s9gjl\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4902035628002\",\\n    \"dateCreated\": \"2025-05-22 07:54:48\",\\n    \"dateModified\": \"2025-05-22 07:54:51\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213366\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925687416-28ww4\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4297563362821\",\\n    \"dateCreated\": \"2025-05-22 07:54:46\",\\n    \"dateModified\": \"2025-05-22 07:54:50\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213364\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925685687-hdbx3\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2075409643113\",\\n    \"dateCreated\": \"2025-05-22 07:54:44\",\\n    \"dateModified\": \"2025-05-22 07:54:48\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213362\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925683886-cygt1\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8278016840767\",\\n    \"dateCreated\": \"2025-05-22 07:54:42\",\\n    \"dateModified\": \"2025-05-22 07:54:46\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213359\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925682023-tg4ce\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8935284562085\",\\n    \"dateCreated\": \"2025-05-22 07:54:41\",\\n    \"dateModified\": \"2025-05-22 07:54:44\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213355\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925680194-hvai6\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4342480688867\",\\n    \"dateCreated\": \"2025-05-22 07:54:39\",\\n    \"dateModified\": \"2025-05-22 07:54:43\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213353\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925678705-ffcb9\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7395637864792\",\\n    \"dateCreated\": \"2025-05-22 07:54:37\",\\n    \"dateModified\": \"2025-05-22 07:54:41\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213348\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925676623-edjiq\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"9175502864800\",\\n    \"dateCreated\": \"2025-05-22 07:54:35\",\\n    \"dateModified\": \"2025-05-22 07:54:39\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213347\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925675044-rywe9\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"9640152417629\",\\n    \"dateCreated\": \"2025-05-22 07:54:34\",\\n    \"dateModified\": \"2025-05-22 07:54:37\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213343\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925673304-ussk0\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8398000675994\",\\n    \"dateCreated\": \"2025-05-22 07:54:32\",\\n    \"dateModified\": \"2025-05-22 07:54:36\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213341\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925671659-jikjf\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3660656426279\",\\n    \"dateCreated\": \"2025-05-22 07:54:30\",\\n    \"dateModified\": \"2025-05-22 07:54:34\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213336\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925669609-l5txw\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8509928982205\",\\n    \"dateCreated\": \"2025-05-22 07:54:28\",\\n    \"dateModified\": \"2025-05-22 07:54:32\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213333\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925667551-5r1jj\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4750893242451\",\\n    \"dateCreated\": \"2025-05-22 07:54:26\",\\n    \"dateModified\": \"2025-05-22 07:54:30\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213332\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925666022-0597q\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2744707142198\",\\n    \"dateCreated\": \"2025-05-22 07:54:25\",\\n    \"dateModified\": \"2025-05-22 07:54:28\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213330\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925664231-l20re\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3843967992890\",\\n    \"dateCreated\": \"2025-05-22 07:54:23\",\\n    \"dateModified\": \"2025-05-22 07:54:27\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213329\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925662655-p2zuc\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4013812111523\",\\n    \"dateCreated\": \"2025-05-22 07:54:21\",\\n    \"dateModified\": \"2025-05-22 07:54:25\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213327\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925661004-9txvs\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3079413715523\",\\n    \"dateCreated\": \"2025-05-19 02:08:07\",\\n    \"dateModified\": \"2025-05-20 02:01:12\",\\n    \"status\": \"no_show\",\\n    \"guestID\": \"138857153\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Eduard Doe Nico\",\\n    \"startDate\": \"2025-05-19\",\\n    \"endDate\": \"2025-05-20\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"ss-903704-1\",\\n    \"sourceName\": \"Default Corporate Client\",\\n    \"thirdPartyIdentifier\": null,\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  }\\n]' annotations=None"], "error": null, "timestamp": "2025-05-25T11:18:50.747702"}, "get_availability_tool": {"success": true, "result": [], "error": null, "timestamp": "2025-05-25T11:18:51.721044"}, "search_guests_tool": {"success": true, "result": [], "error": null, "timestamp": "2025-05-25T11:18:52.723584"}, "create_reservation_tool": {"success": true, "result": ["type='text' text='{\\n  \"message\": \"Reservation creation not implemented yet\"\\n}' annotations=None"], "error": null, "timestamp": "2025-05-25T11:18:52.728590"}, "get_reservation_tool": {"success": true, "result": [], "error": null, "timestamp": "2025-05-25T11:18:53.757008"}, "get_invoice_tool": {"success": true, "result": [], "error": null, "timestamp": "2025-05-25T11:18:54.993766"}, "get_guest_tool": {"success": true, "result": [], "error": null, "timestamp": "2025-05-25T11:18:56.003356"}}, "resources": {"recent_reservations": {"success": true, "result": ["uri=AnyUrl('cloudbeds://reservations/recent') mimeType='text/plain' text='[\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"5113613993769\",\\n    \"dateCreated\": \"2025-05-22 13:43:41\",\\n    \"dateModified\": \"2025-05-25 02:01:18\",\\n    \"status\": \"no_show\",\\n    \"guestID\": \"139240972\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-05-22\",\\n    \"endDate\": \"2025-05-23\",\\n    \"adults\": \"2\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"server-1747946620100-182\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8345540431982\",\\n    \"dateCreated\": \"2025-05-22 07:55:03\",\\n    \"dateModified\": \"2025-05-22 07:55:07\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213392\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925702566-s4w5l\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"6971110202035\",\\n    \"dateCreated\": \"2025-05-22 07:55:01\",\\n    \"dateModified\": \"2025-05-22 07:55:05\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213382\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925700588-8z4mn\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"1928974196042\",\\n    \"dateCreated\": \"2025-05-22 07:54:59\",\\n    \"dateModified\": \"2025-05-22 07:55:03\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213379\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925698501-ydlbt\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7927359142743\",\\n    \"dateCreated\": \"2025-05-22 07:54:57\",\\n    \"dateModified\": \"2025-05-22 07:55:01\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213378\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925696459-k9o0f\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7345756948254\",\\n    \"dateCreated\": \"2025-05-22 07:54:55\",\\n    \"dateModified\": \"2025-05-22 07:54:59\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213375\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925694505-ytyt3\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4436526719516\",\\n    \"dateCreated\": \"2025-05-22 07:54:53\",\\n    \"dateModified\": \"2025-05-22 07:54:57\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213373\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925692569-uz3pc\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2183375888922\",\\n    \"dateCreated\": \"2025-05-22 07:54:51\",\\n    \"dateModified\": \"2025-05-22 07:54:55\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213370\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925690768-lxj0f\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"1363735446951\",\\n    \"dateCreated\": \"2025-05-22 07:54:49\",\\n    \"dateModified\": \"2025-05-22 07:54:53\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213369\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925688990-s9gjl\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4902035628002\",\\n    \"dateCreated\": \"2025-05-22 07:54:48\",\\n    \"dateModified\": \"2025-05-22 07:54:51\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213366\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925687416-28ww4\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4297563362821\",\\n    \"dateCreated\": \"2025-05-22 07:54:46\",\\n    \"dateModified\": \"2025-05-22 07:54:50\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213364\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925685687-hdbx3\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2075409643113\",\\n    \"dateCreated\": \"2025-05-22 07:54:44\",\\n    \"dateModified\": \"2025-05-22 07:54:48\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213362\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925683886-cygt1\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8278016840767\",\\n    \"dateCreated\": \"2025-05-22 07:54:42\",\\n    \"dateModified\": \"2025-05-22 07:54:46\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213359\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925682023-tg4ce\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8935284562085\",\\n    \"dateCreated\": \"2025-05-22 07:54:41\",\\n    \"dateModified\": \"2025-05-22 07:54:44\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213355\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925680194-hvai6\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4342480688867\",\\n    \"dateCreated\": \"2025-05-22 07:54:39\",\\n    \"dateModified\": \"2025-05-22 07:54:43\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213353\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925678705-ffcb9\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"7395637864792\",\\n    \"dateCreated\": \"2025-05-22 07:54:37\",\\n    \"dateModified\": \"2025-05-22 07:54:41\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213348\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925676623-edjiq\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"9175502864800\",\\n    \"dateCreated\": \"2025-05-22 07:54:35\",\\n    \"dateModified\": \"2025-05-22 07:54:39\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213347\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925675044-rywe9\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"9640152417629\",\\n    \"dateCreated\": \"2025-05-22 07:54:34\",\\n    \"dateModified\": \"2025-05-22 07:54:37\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213343\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925673304-ussk0\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8398000675994\",\\n    \"dateCreated\": \"2025-05-22 07:54:32\",\\n    \"dateModified\": \"2025-05-22 07:54:36\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213341\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925671659-jikjf\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3660656426279\",\\n    \"dateCreated\": \"2025-05-22 07:54:30\",\\n    \"dateModified\": \"2025-05-22 07:54:34\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213336\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925669609-l5txw\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"8509928982205\",\\n    \"dateCreated\": \"2025-05-22 07:54:28\",\\n    \"dateModified\": \"2025-05-22 07:54:32\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213333\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-5\",\\n    \"sourceName\": \"Email\",\\n    \"thirdPartyIdentifier\": \"test-1747925667551-5r1jj\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4750893242451\",\\n    \"dateCreated\": \"2025-05-22 07:54:26\",\\n    \"dateModified\": \"2025-05-22 07:54:30\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213332\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925666022-0597q\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"2744707142198\",\\n    \"dateCreated\": \"2025-05-22 07:54:25\",\\n    \"dateModified\": \"2025-05-22 07:54:28\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213330\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-2\",\\n    \"sourceName\": \"Walk-In\",\\n    \"thirdPartyIdentifier\": \"test-1747925664231-l20re\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3843967992890\",\\n    \"dateCreated\": \"2025-05-22 07:54:23\",\\n    \"dateModified\": \"2025-05-22 07:54:27\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213329\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-07-21\",\\n    \"endDate\": \"2025-07-23\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 1000,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925662655-p2zuc\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"4013812111523\",\\n    \"dateCreated\": \"2025-05-22 07:54:21\",\\n    \"dateModified\": \"2025-05-22 07:54:25\",\\n    \"status\": \"confirmed\",\\n    \"guestID\": \"139213327\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Test User\",\\n    \"startDate\": \"2025-06-21\",\\n    \"endDate\": \"2025-06-22\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"s-1\",\\n    \"sourceName\": \"Website/Booking Engine\",\\n    \"thirdPartyIdentifier\": \"test-1747925661004-9txvs\",\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  },\\n  {\\n    \"propertyID\": \"317353\",\\n    \"reservationID\": \"3079413715523\",\\n    \"dateCreated\": \"2025-05-19 02:08:07\",\\n    \"dateModified\": \"2025-05-20 02:01:12\",\\n    \"status\": \"no_show\",\\n    \"guestID\": \"138857153\",\\n    \"profileID\": \"\",\\n    \"guestName\": \"Eduard Doe Nico\",\\n    \"startDate\": \"2025-05-19\",\\n    \"endDate\": \"2025-05-20\",\\n    \"adults\": \"1\",\\n    \"children\": \"0\",\\n    \"balance\": 500,\\n    \"sourceID\": \"ss-903704-1\",\\n    \"sourceName\": \"Default Corporate Client\",\\n    \"thirdPartyIdentifier\": null,\\n    \"allotmentBlockCode\": null,\\n    \"origin\": \"\"\\n  }\\n]'"], "error": null, "timestamp": "2025-05-25T11:18:57.137017"}, "room_types": {"success": true, "result": ["uri=AnyUrl('cloudbeds://rooms/types') mimeType='text/plain' text='[\\n  {\\n    \"roomTypeID\": \"653496\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Junior Suite Sea View\",\\n    \"roomTypeNameShort\": \"JVM\",\\n    \"roomTypeDescription\": \"<div><div>The ocean suite offers a spectacular view of the sea of \\\\n\\u200b\\u200bEssaouira. Enjoy the cool breeze and the sound of the waves in this \\\\nspacious room, elegantly decorated in a modern Moroccan style, thus \\\\nproviding an atmosphere of peace and relaxation.</div></div><br>\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 3,\\n    \"adultsIncluded\": 2,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": {\\n      \"2\": \"Bathrobes\",\\n      \"4\": \"Cable television\",\\n      \"5\": \"Ceiling fan\",\\n      \"8\": \"Coffee maker\",\\n      \"7\": \"Cribs upon request\",\\n      \"6\": \"Hairdryer\",\\n      \"9\": \"Minibar\",\\n      \"1\": \"Safe Box\",\\n      \"3\": \"Slippers\",\\n      \"10\": \"Wireless internet (WiFi)\",\\n      \"0\": \"Work Zone\"\\n    },\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 6\\n  },\\n  {\\n    \"roomTypeID\": \"653497\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Ocean Deluxe\",\\n    \"roomTypeNameShort\": \"OD\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 5,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  },\\n  {\\n    \"roomTypeID\": \"653498\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Garden Junior\",\\n    \"roomTypeNameShort\": \"GJ\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 2,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  },\\n  {\\n    \"roomTypeID\": \"653499\",\\n    \"propertyID\": \"317353\",\\n    \"roomTypeName\": \"Ocean Junior\",\\n    \"roomTypeNameShort\": \"OJ\",\\n    \"roomTypeDescription\": \"Test\",\\n    \"isPrivate\": true,\\n    \"maxGuests\": 2,\\n    \"adultsIncluded\": 1,\\n    \"childrenIncluded\": 0,\\n    \"roomTypePhotos\": [],\\n    \"roomTypeFeatures\": [],\\n    \"roomsAvailable\": 0,\\n    \"roomTypeUnits\": 5\\n  }\\n]'"], "error": null, "timestamp": "2025-05-25T11:18:58.369067"}, "rooms": {"success": true, "result": ["uri=AnyUrl('cloudbeds://rooms') mimeType='text/plain' text='[\\n  {\\n    \"propertyID\": \"317353\",\\n    \"rooms\": [\\n      {\\n        \"roomID\": \"653496-0\",\\n        \"roomName\": \"JVM(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-1\",\\n        \"roomName\": \"JVM(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-2\",\\n        \"roomName\": \"JVM(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-3\",\\n        \"roomName\": \"JVM(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-4\",\\n        \"roomName\": \"JVM(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653496-5\",\\n        \"roomName\": \"JVM(6)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 3,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653496\",\\n        \"roomTypeName\": \"Junior Suite Sea View\",\\n        \"roomTypeNameShort\": \"JVM\"\\n      },\\n      {\\n        \"roomID\": \"653497-0\",\\n        \"roomName\": \"OD(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-1\",\\n        \"roomName\": \"OD(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-2\",\\n        \"roomName\": \"OD(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-3\",\\n        \"roomName\": \"OD(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653497-4\",\\n        \"roomName\": \"OD(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 5,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653497\",\\n        \"roomTypeName\": \"Ocean Deluxe\",\\n        \"roomTypeNameShort\": \"OD\"\\n      },\\n      {\\n        \"roomID\": \"653498-0\",\\n        \"roomName\": \"GJ(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-1\",\\n        \"roomName\": \"GJ(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-2\",\\n        \"roomName\": \"GJ(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-3\",\\n        \"roomName\": \"GJ(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653498-4\",\\n        \"roomName\": \"GJ(5)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653498\",\\n        \"roomTypeName\": \"Garden Junior\",\\n        \"roomTypeNameShort\": \"GJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-0\",\\n        \"roomName\": \"OJ(1)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-1\",\\n        \"roomName\": \"OJ(2)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-2\",\\n        \"roomName\": \"OJ(3)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      },\\n      {\\n        \"roomID\": \"653499-3\",\\n        \"roomName\": \"OJ(4)\",\\n        \"roomDescription\": \"\",\\n        \"maxGuests\": 2,\\n        \"isPrivate\": true,\\n        \"isVirtual\": false,\\n        \"roomBlocked\": false,\\n        \"roomTypeID\": \"653499\",\\n        \"roomTypeName\": \"Ocean Junior\",\\n        \"roomTypeNameShort\": \"OJ\"\\n      }\\n    ]\\n  }\\n]'"], "error": null, "timestamp": "2025-05-25T11:18:59.453505"}, "availability_next30days": {"success": true, "result": ["uri=AnyUrl('cloudbeds://availability/next30days') mimeType='text/plain' text='[]'"], "error": null, "timestamp": "2025-05-25T11:19:00.443794"}, "property": {"success": true, "result": ["uri=AnyUrl('cloudbeds://property') mimeType='text/plain' text='[]'"], "error": null, "timestamp": "2025-05-25T11:19:01.471180"}, "specific_reservation": {"success": true, "result": ["uri=AnyUrl('cloudbeds://reservations/test123') mimeType='text/plain' text='[]'"], "error": null, "timestamp": "2025-05-25T11:19:02.672933"}}, "prompts": {"summarize_reservation": {"success": false, "result": null, "error": "1 validation error for GetPromptRequestParams\narguments.reservation_data\n  Input should be a valid string [type=string_type, input_value={'reservationID': '12345'..., 'status': 'confirmed'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "timestamp": "2025-05-25T11:19:02.725367"}, "new_reservation_prompt": {"success": true, "result": "meta=None description='Generate a prompt for creating a new reservation.' messages=[PromptMessage(role='user', content=TextContent(type='text', text='\\nPlease help me create a new reservation in Cloudbeds. I need the following information:\\n\\n1. Guest name\\n2. Guest email\\n3. Guest phone\\n4. Check-in date (YYYY-MM-DD)\\n5. Check-out date (YYYY-MM-DD)\\n6. Number of adults\\n7. Number of children\\n8. Room type or specific room\\n9. Rate plan\\n10. Any special requests or notes\\n\\nPlease format this information in a structured way that can be used to create the reservation.\\n', annotations=None))]", "error": null, "timestamp": "2025-05-25T11:19:02.729362"}}, "summary": {"total_tests": 17, "passed": 16, "failed": 1, "errors": ["prompts: summarize_reservation - 1 validation error for GetPromptRequestParams\narguments.reservation_data\n  Input should be a valid string [type=string_type, input_value={'reservationID': '12345'..., 'status': 'confirmed'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type"]}}