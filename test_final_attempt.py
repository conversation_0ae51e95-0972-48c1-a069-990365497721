"""
Final attempt - test with exact form data structure without payment method.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_final_attempt():
    """Final attempt with exact form data structure."""
    print("🎯 Final Attempt - Exact Form Data Structure...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Exact form data structure that should work
    form_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"final.attempt.{int(asyncio.get_event_loop().time())}@example.com",
        "guestPhone": "+34123456789",
        "guestAddress": "Calle Mayor 123",
        "guestCity": "Madrid",
        "guestCountry": "ES",
        "guestPostalCode": "28001",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-final-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": "true",
        "sourceID": "s-2-1",
        "rooms": json.dumps([{
            "roomTypeID": "653498",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "adults": 2,
            "children": 0,
            "roomName": "Garden Deluxe"
        }])
    }
    
    print(f"\n📋 Form Data Structure:")
    for key, value in form_data.items():
        print(f"   {key}: {value}")
    
    try:
        print(f"\n🚀 Testing final form data structure...")
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=form_data, headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created without payment method!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    print(f"   Confirmation Code: {data.get('confirmationCode')}")
                    print(f"   Full response: {json.dumps(data, indent=2)}")
                    return True
                elif "paymentMethod is required" in data.get('message', ''):
                    print(f"\n💡 Payment method is required. Let's try some approaches...")
                    
                    # Try different payment method approaches
                    payment_methods = [
                        # Try numeric IDs (maybe payment methods are IDs)
                        "1", "2", "3", "4", "5",
                        # Try without quotes
                        1, 2, 3, 4, 5,
                        # Try empty string
                        "",
                        # Try null/None
                        None,
                        # Try boolean
                        True, False
                    ]
                    
                    for payment_method in payment_methods:
                        print(f"\n   🔄 Trying payment method: {payment_method} (type: {type(payment_method).__name__})")
                        
                        test_data = form_data.copy()
                        if payment_method is not None:
                            test_data["paymentMethod"] = str(payment_method) if payment_method is not None else ""
                        
                        response2 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                        if response2.status_code == 200:
                            data2 = response2.json()
                            print(f"      Success: {data2.get('success')}")
                            print(f"      Message: {data2.get('message')}")
                            
                            if data2.get('success'):
                                print(f"\n🎉 SUCCESS! Payment method '{payment_method}' works!")
                                print(f"      Reservation ID: {data2.get('reservationID')}")
                                print(f"      Confirmation Code: {data2.get('confirmationCode')}")
                                return True
                            elif "Invalid Parameter Format" not in data2.get('message', ''):
                                print(f"      ✅ Payment method '{payment_method}' accepted (different error)")
                                if "paymentMethod" not in data2.get('message', ''):
                                    print(f"      📝 New error: {data2.get('message')}")
                else:
                    print(f"\n📝 Different error: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_final_attempt())
    if success:
        print(f"\n🎉 FINAL SUCCESS! RESERVATION CREATION WORKING!")
    else:
        print(f"\n🔧 STILL NEED TO FIND THE RIGHT PAYMENT METHOD FORMAT")
