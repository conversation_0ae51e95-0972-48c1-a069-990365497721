"""
Test the updated implementation based on the working technical README.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_working_implementation():
    """Test the updated implementation with the working pattern."""
    print("🎯 Testing Updated Implementation Based on Working Technical README")
    print("=" * 70)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Test with the exact structure from the working implementation
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Use the structure from the working implementation README
        working_reservation = {
            # Guest information (as per working implementation)
            "firstName": "John",
            "lastName": "Doe",
            "email": f"working.test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34123456789",
            "address": "Calle Mayor 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001",
            
            # Room information (as per working implementation)
            "roomTypeID": "653498",  # <PERSON> Junior - verified valid
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0,
            
            # Optional fields (as per working implementation)
            "status": "confirmed",
            "sendEmailConfirmation": True,
            "sourceID": "s-2-1",  # Format from working implementation
            "thirdPartyIdentifier": f"mcp-test-{int(datetime.now().timestamp())}"
        }
        
        print(f"\n📋 Testing with Working Implementation Structure:")
        for key, value in working_reservation.items():
            print(f"   {key}: {value}")
        
        try:
            print(f"\n🚀 Calling create_reservation_tool with working structure...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": working_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created successfully!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    print(f"   Third Party ID: {response_data.get('thirdPartyIdentifier')}")
                    
                    # Verify the reservation was created by getting its details
                    if response_data.get('reservationID'):
                        print(f"\n🔍 Verifying reservation creation...")
                        try:
                            verify_result = await client.call_tool("get_reservation_tool", {
                                "reservation_id": response_data['reservationID']
                            })
                            if verify_result and verify_result[0].text:
                                verify_data = json.loads(verify_result[0].text)
                                if verify_data.get('success') is not False:
                                    print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                                else:
                                    print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                        except Exception as ve:
                            print(f"   ⚠️  Verification error: {ve}")
                    
                    return True
                else:
                    print(f"\n❌ Failed: {response_data.get('message')}")
                    print(f"   Error details: {response_data.get('error')}")
                    
                    # Check if we're still getting the same errors
                    if "Invalid Parameter Format" in response_data.get('message', ''):
                        print(f"\n🔍 Still getting 'Invalid Parameter Format' error")
                        print(f"   This suggests the API structure might still need adjustment")
                    elif "paymentMethod" in response_data.get('message', ''):
                        print(f"\n✅ Progress! We've moved past the previous errors")
                        print(f"   Now we need to handle the payment method requirement")
            else:
                print(f"\n⚠️  Empty or invalid response")
                
        except Exception as e:
            print(f"\n💥 Exception: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Test 2: Try with minimal data to see what's required
        print(f"\n" + "=" * 70)
        print(f"📋 Test 2: Minimal Data Structure")
        
        minimal_reservation = {
            "firstName": "Jane",
            "lastName": "Smith",
            "email": f"minimal.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after
        }
        
        print(f"\n📋 Minimal reservation data:")
        for key, value in minimal_reservation.items():
            print(f"   {key}: {value}")
        
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": minimal_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Minimal Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS with minimal data!")
                    return True
                else:
                    print(f"\n📝 Error with minimal data: {response_data.get('message')}")
        except Exception as e:
            print(f"\n💥 Minimal test exception: {str(e)}")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_working_implementation())
    if success:
        print(f"\n🎉 IMPLEMENTATION SUCCESSFUL!")
    else:
        print(f"\n🔧 IMPLEMENTATION NEEDS MORE WORK")
