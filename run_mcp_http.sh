#!/bin/bash
# Script to run the Cloudbeds MCP server with HTTP transport

# Default values
TRANSPORT="streamable-http"
HOST="localhost"
PORT="8000"
PATH="/mcp"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --transport)
      TRANSPORT="$2"
      shift 2
      ;;
    --host)
      HOST="$2"
      shift 2
      ;;
    --port)
      PORT="$2"
      shift 2
      ;;
    --path)
      PATH="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
  echo "Activating virtual environment..."
  source .venv/bin/activate
fi

# Run the MCP server
echo "Starting Cloudbeds MCP server with $TRANSPORT transport..."
echo "URL: http://$HOST:$PORT$PATH"

python main.py --transport "$TRANSPORT" --host "$HOST" --port "$PORT" --path "$PATH"
