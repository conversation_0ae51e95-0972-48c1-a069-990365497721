"""
Test final rooms parameter formats.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_rooms_formats_final():
    """Test final rooms parameter formats."""
    print("🎯 Testing Final Rooms Parameter Formats...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "John",
        "guestLastName": "Doe",
        "guestEmail": f"final.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "paymentMethod": "Cash"
    }
    
    # Test different rooms formats
    rooms_formats = [
        # Test 1: Simple string - just the room type ID
        ("Simple string", "653498"),
        
        # Test 2: Number
        ("Number", 653498),
        
        # Test 3: Array of strings
        ("Array of strings", ["653498"]),
        
        # Test 4: Comma-separated
        ("Comma-separated", "653498,2,0"),
        
        # Test 5: Pipe-separated
        ("Pipe-separated", "653498|2|0"),
        
        # Test 6: Form-like string
        ("Form-like", "roomTypeID=653498&adults=2&children=0"),
        
        # Test 7: Simple object as string (not JSON)
        ("Object string", "roomTypeID:653498,adults:2,children:0"),
        
        # Test 8: Just "1" (indicating 1 room)
        ("Just 1", "1"),
        
        # Test 9: Empty string
        ("Empty string", ""),
        
        # Test 10: Array with just room type
        ("Array with room type", [653498])
    ]
    
    for name, rooms_value in rooms_formats:
        print(f"\n📡 Testing {name}: {rooms_value}")
        try:
            test_data = base_data.copy()
            
            # Handle different types
            if isinstance(rooms_value, (list, dict)):
                test_data["rooms"] = json.dumps(rooms_value)
            else:
                test_data["rooms"] = str(rooms_value)
            
            test_data["guestEmail"] = f"final.{name.replace(' ', '')}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Format '{name}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        print(f"   Full response: {json.dumps(data, indent=2)}")
                        return rooms_value
                    elif "Invalid Parameter Format" not in data.get('message', '') and "rooms" not in data.get('message', ''):
                        print(f"   ✅ Rooms format accepted, other issue: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No valid rooms format found")
    return None

if __name__ == "__main__":
    asyncio.run(test_rooms_formats_final())
