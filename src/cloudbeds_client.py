"""
Cloudbeds API client module.

This module provides functions to interact with the Cloudbeds API.
"""

import time
import logging
import datetime
from typing import Dict, List, Optional, Any, Union

import httpx

from src.config import CLOUDBEDS_API_KEY, CLOUDBEDS_PROPERTY_ID, API_BASE_URL, API_RATE_LIMIT

# Set up logging
logger = logging.getLogger("cloudbeds_client")

def get_access_token() -> str:
    """
    Get access token from Cloudbeds API using API key.

    Returns:
        str: Access token
    """
    # For the Cloudbeds API, we can use the API key directly as the access token
    return CLOUDBEDS_API_KEY

async def api_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    property_id: Optional[str] = None,
    method: str = "GET",
    data: Optional[Dict[str, Any]] = None
) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Make an async request to the Cloudbeds API with rate limiting.

    Args:
        endpoint (str): API endpoint
        params (dict, optional): Request parameters for GET requests
        property_id (str, optional): Cloudbeds property ID
        method (str): HTTP method (GET, POST, PUT, DELETE)
        data (dict, optional): Request body data for POST/PUT requests

    Returns:
        Union[List[Dict[str, Any]], Dict[str, Any]]: API response data
    """
    url = f"{API_BASE_URL}/{endpoint}"

    # Initialize parameters if not provided
    if params is None:
        params = {}

    # Initialize data if not provided
    if data is None:
        data = {}

    # Add property ID to appropriate location based on method
    if method.upper() == "GET":
        # For GET requests, add to params
        if property_id:
            params["propertyID"] = property_id
        elif CLOUDBEDS_PROPERTY_ID:
            params["propertyID"] = CLOUDBEDS_PROPERTY_ID
    else:
        # For POST/PUT requests, add to data
        if property_id:
            data["propertyID"] = property_id
        elif CLOUDBEDS_PROPERTY_ID:
            data["propertyID"] = CLOUDBEDS_PROPERTY_ID

    # Request headers - use x-api-key authentication as per working implementation
    # The working implementation uses JSON to their own API, but Cloudbeds API might expect form data
    if method.upper() in ["POST", "PUT"] and endpoint == "postReservation":
        headers = {
            "x-api-key": get_access_token(),
            "X-PROPERTY-ID": property_id or CLOUDBEDS_PROPERTY_ID,
            "Content-Type": "application/x-www-form-urlencoded"  # Use form data for reservations
        }
    else:
        headers = {
            "x-api-key": get_access_token(),
            "X-PROPERTY-ID": property_id or CLOUDBEDS_PROPERTY_ID,
            "Content-Type": "application/json"
        }

    try:
        # Make API request
        async with httpx.AsyncClient() as client:
            if method.upper() == "GET":
                response = await client.get(url, params=params, headers=headers)
            elif method.upper() == "POST":
                # Debug logging for POST requests
                logger.info(f"POST request to {url}")
                logger.info(f"Headers: {headers}")

                # Handle different content types based on endpoint
                if endpoint == "postReservation" and headers.get("Content-Type") == "application/x-www-form-urlencoded":
                    # Convert nested objects to form data for reservation creation
                    form_data = {}
                    for key, value in data.items():
                        if isinstance(value, (dict, list)):
                            import json
                            form_data[key] = json.dumps(value)
                        else:
                            form_data[key] = str(value)

                    logger.info(f"Form Data: {form_data}")
                    logger.info(f"Form Data Keys: {list(form_data.keys())}")
                    logger.info(f"Form Data Values: {[str(v)[:100] for v in form_data.values()]}")
                    response = await client.post(url, data=form_data, headers=headers)
                else:
                    logger.info(f"JSON Data: {data}")
                    response = await client.post(url, json=data, headers=headers)
            elif method.upper() == "PUT":
                # Send JSON data for PUT requests too
                response = await client.put(url, json=data, headers=headers)
            elif method.upper() == "DELETE":
                response = await client.delete(url, params=params, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

        # Respect rate limit
        await asyncio.sleep(1.0 / API_RATE_LIMIT)

        # Check if request was successful
        if response.status_code in [200, 201]:
            data_response = response.json()

            # Debug logging for responses
            if method.upper() == "POST":
                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response data: {data_response}")

            if data_response.get('success'):
                return data_response.get('data', data_response)
            else:
                logger.error(f"API returned error for {endpoint}: {data_response.get('message')}")
                return data_response  # Return the error response for proper handling
        else:
            logger.error(f"API request failed for {endpoint} with status code {response.status_code}: {response.text}")
            return {"success": False, "message": f"HTTP {response.status_code}: {response.text}"}
    except Exception as e:
        logger.error(f"Error making API request to {endpoint}: {str(e)}")
        return {"success": False, "message": str(e)}

# Add missing import
import asyncio
