"""
Guest tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds guests.
"""

from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_guest_details(
    guest_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific guest.

    Args:
        guest_id (str): Guest ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed guest data
    """
    # Request parameters
    params = {
        "guestID": guest_id
    }

    return await api_request("getGuest", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def search_guests(
    search_term: str,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for guests by name, email, or phone.

    Args:
        search_term (str): Search term
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of matching guest data
    """
    # Request parameters
    params = {
        "term": search_term
    }

    return await api_request("searchGuests", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_guest(
    guest_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new guest in Cloudbeds.

    Args:
        guest_data (dict): Guest data
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created guest data
    """
    # This would need to be implemented with a POST request
    # For now, we'll return a placeholder
    return {"message": "Guest creation not implemented yet"}

async def update_guest(
    guest_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing guest in Cloudbeds.

    Args:
        guest_id (str): Guest ID
        update_data (dict): Data to update
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated guest data
    """
    # This would need to be implemented with a PUT request
    # For now, we'll return a placeholder
    return {"message": "Guest update not implemented yet"}
