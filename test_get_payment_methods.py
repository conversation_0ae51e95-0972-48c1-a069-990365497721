"""
Test getting payment methods from the API first.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_get_payment_methods():
    """Get payment methods from the API first."""
    print("🎯 Getting Payment Methods from API...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # Try to get payment methods
            print(f"\n📡 Getting payment methods...")
            response = await client.get(f"{API_BASE_URL}/getPaymentMethods", headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Payment Methods: {json.dumps(data, indent=2)}")
                
                if data.get('success') and data.get('data'):
                    payment_methods = data.get('data', [])
                    print(f"\n💡 Found {len(payment_methods)} payment methods:")
                    
                    for pm in payment_methods:
                        print(f"   - {pm}")
                    
                    # Now try to create a reservation with the first valid payment method
                    if payment_methods:
                        first_payment_method = payment_methods[0]
                        print(f"\n🔄 Testing reservation with payment method: {first_payment_method}")
                        
                        # Test reservation creation with valid payment method
                        form_data = {
                            "propertyID": CLOUDBEDS_PROPERTY_ID,
                            "guestFirstName": "John",
                            "guestLastName": "Doe",
                            "guestEmail": f"payment.test.{int(asyncio.get_event_loop().time())}@example.com",
                            "guestPhone": "+34123456789",
                            "guestCountry": "ES",
                            "startDate": "2025-05-26",
                            "endDate": "2025-05-27",
                            "adults": "2",
                            "children": "0",
                            "roomTypeID": "653498",
                            "status": "confirmed",
                            "thirdPartyIdentifier": f"mcp-payment-{int(asyncio.get_event_loop().time())}",
                            "sendEmailConfirmation": "true",
                            "sourceID": "s-2-1",
                            "paymentMethod": str(first_payment_method),
                            "rooms": json.dumps([{
                                "roomTypeID": "653498",
                                "startDate": "2025-05-26",
                                "endDate": "2025-05-27",
                                "adults": 2,
                                "children": 0
                            }])
                        }
                        
                        headers_form = {
                            "x-api-key": CLOUDBEDS_API_KEY,
                            "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
                            "Content-Type": "application/x-www-form-urlencoded"
                        }
                        
                        response2 = await client.post(f"{API_BASE_URL}/postReservation", data=form_data, headers=headers_form)
                        if response2.status_code == 200:
                            data2 = response2.json()
                            print(f"      Success: {data2.get('success')}")
                            print(f"      Message: {data2.get('message')}")
                            
                            if data2.get('success'):
                                print(f"\n🎉 SUCCESS! Reservation created with valid payment method!")
                                print(f"      Reservation ID: {data2.get('reservationID')}")
                                return True
                            else:
                                print(f"      ❌ Still failed: {data2.get('message')}")
                        
                        # Try with different formats of the payment method
                        if isinstance(first_payment_method, dict):
                            for key in ['id', 'code', 'name', 'value']:
                                if key in first_payment_method:
                                    test_value = first_payment_method[key]
                                    print(f"\n   🔄 Trying payment method {key}: {test_value}")
                                    
                                    form_data["paymentMethod"] = str(test_value)
                                    response3 = await client.post(f"{API_BASE_URL}/postReservation", data=form_data, headers=headers_form)
                                    if response3.status_code == 200:
                                        data3 = response3.json()
                                        if data3.get('success'):
                                            print(f"         🎉 SUCCESS with {key}: {test_value}!")
                                            return True
                                        else:
                                            print(f"         Message: {data3.get('message')}")
                
            else:
                print(f"   Error getting payment methods: {response.status_code}")
                print(f"   Response: {response.text}")
            
            # Also try getPaymentsCapabilities
            print(f"\n📡 Getting payment capabilities...")
            response = await client.get(f"{API_BASE_URL}/getPaymentsCapabilities", headers=headers)
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   Capabilities: {json.dumps(data, indent=2)}")
            
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_get_payment_methods())
    if success:
        print(f"\n🎉 FOUND WORKING PAYMENT METHOD!")
    else:
        print(f"\n🔧 PAYMENT METHOD ISSUE PERSISTS")
