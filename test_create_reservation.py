"""
Comprehensive test suite for the create_reservation function.

This script tests the create_reservation implementation with various scenarios:
- Valid reservation creation
- Missing required fields validation
- Invalid date format validation
- Date logic validation (end date after start date)
- Room assignment validation
- Integration with MCP tool
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

class CreateReservationTester:
    """Test suite for create_reservation functionality."""
    
    def __init__(self, url: str = "http://localhost:8000/mcp"):
        self.url = url
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: dict = None):
        """Log test result."""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
        if message:
            print(f"   {message}")
        if not success and data:
            print(f"   Response: {data}")
    
    async def test_valid_reservation_creation(self, client: Client):
        """Test creating a valid reservation."""
        print("\n🎯 Testing Valid Reservation Creation...")
        
        # Get room types first to use a valid roomTypeID
        try:
            room_types = await client.call_tool("get_room_types_tool")
            if room_types and room_types[0].text:
                room_data = json.loads(room_types[0].text)
                if room_data:
                    room_type_id = room_data[0]['roomTypeID']
                    print(f"   Using roomTypeID: {room_type_id}")
                else:
                    room_type_id = "653496"  # Fallback
                    print(f"   Using fallback roomTypeID: {room_type_id}")
            else:
                room_type_id = "653496"  # Fallback
                print(f"   Using fallback roomTypeID: {room_type_id}")
        except Exception as e:
            room_type_id = "653496"  # Fallback
            print(f"   Error getting room types, using fallback: {e}")
        
        # Create a valid reservation
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        valid_reservation = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"john.doe.test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+1234567890",
            "startDate": tomorrow,
            "endDate": day_after,
            "roomTypeID": room_type_id,
            "adults": 2,
            "children": 0,
            "notes": "Test reservation created via MCP API"
        }
        
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": valid_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                
                if response_data.get('success') is False:
                    self.log_test(
                        "Valid Reservation Creation",
                        False,
                        f"API returned error: {response_data.get('message')}",
                        response_data
                    )
                else:
                    # Check if we got a reservation ID or success indicator
                    if 'reservationID' in response_data or response_data.get('success') is True:
                        self.log_test(
                            "Valid Reservation Creation",
                            True,
                            f"Reservation created successfully",
                            response_data
                        )
                        return response_data.get('reservationID')
                    else:
                        self.log_test(
                            "Valid Reservation Creation",
                            False,
                            "No reservation ID returned",
                            response_data
                        )
            else:
                self.log_test(
                    "Valid Reservation Creation",
                    False,
                    "Empty response from API"
                )
        except Exception as e:
            self.log_test(
                "Valid Reservation Creation",
                False,
                f"Exception: {str(e)}"
            )
        
        return None
    
    async def test_missing_required_fields(self, client: Client):
        """Test validation of missing required fields."""
        print("\n🔍 Testing Missing Required Fields Validation...")
        
        test_cases = [
            {
                "name": "Missing firstName",
                "data": {
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-02",
                    "roomTypeID": "653496"
                },
                "expected_error": "firstName"
            },
            {
                "name": "Missing lastName",
                "data": {
                    "firstName": "John",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-02",
                    "roomTypeID": "653496"
                },
                "expected_error": "lastName"
            },
            {
                "name": "Missing email",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-02",
                    "roomTypeID": "653496"
                },
                "expected_error": "email"
            },
            {
                "name": "Missing startDate",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "endDate": "2025-06-02",
                    "roomTypeID": "653496"
                },
                "expected_error": "startDate"
            },
            {
                "name": "Missing endDate",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-01",
                    "roomTypeID": "653496"
                },
                "expected_error": "endDate"
            }
        ]
        
        for test_case in test_cases:
            try:
                result = await client.call_tool("create_reservation_tool", {
                    "reservation_data": test_case["data"]
                })
                
                if result and result[0].text:
                    response_data = json.loads(result[0].text)
                    
                    if (response_data.get('success') is False and 
                        test_case["expected_error"] in response_data.get('message', '')):
                        self.log_test(
                            test_case["name"],
                            True,
                            f"Correctly rejected: {response_data.get('message')}"
                        )
                    else:
                        self.log_test(
                            test_case["name"],
                            False,
                            f"Unexpected response: {response_data}",
                            response_data
                        )
                else:
                    self.log_test(
                        test_case["name"],
                        False,
                        "Empty response"
                    )
            except Exception as e:
                self.log_test(
                    test_case["name"],
                    False,
                    f"Exception: {str(e)}"
                )
    
    async def test_date_validation(self, client: Client):
        """Test date format and logic validation."""
        print("\n📅 Testing Date Validation...")
        
        test_cases = [
            {
                "name": "Invalid date format",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025/06/01",  # Wrong format
                    "endDate": "2025-06-02",
                    "roomTypeID": "653496"
                },
                "expected_error": "Invalid date format"
            },
            {
                "name": "End date before start date",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-02",
                    "endDate": "2025-06-01",  # Before start date
                    "roomTypeID": "653496"
                },
                "expected_error": "End date must be after start date"
            },
            {
                "name": "Same start and end date",
                "data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-01",  # Same date
                    "roomTypeID": "653496"
                },
                "expected_error": "End date must be after start date"
            }
        ]
        
        for test_case in test_cases:
            try:
                result = await client.call_tool("create_reservation_tool", {
                    "reservation_data": test_case["data"]
                })
                
                if result and result[0].text:
                    response_data = json.loads(result[0].text)
                    
                    if (response_data.get('success') is False and 
                        test_case["expected_error"] in response_data.get('message', '')):
                        self.log_test(
                            test_case["name"],
                            True,
                            f"Correctly rejected: {response_data.get('message')}"
                        )
                    else:
                        self.log_test(
                            test_case["name"],
                            False,
                            f"Unexpected response: {response_data}",
                            response_data
                        )
                else:
                    self.log_test(
                        test_case["name"],
                        False,
                        "Empty response"
                    )
            except Exception as e:
                self.log_test(
                    test_case["name"],
                    False,
                    f"Exception: {str(e)}"
                )
    
    async def test_room_assignment_validation(self, client: Client):
        """Test room assignment validation."""
        print("\n🏨 Testing Room Assignment Validation...")
        
        # Test missing room assignment
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": {
                    "firstName": "John",
                    "lastName": "Doe",
                    "email": "<EMAIL>",
                    "startDate": "2025-06-01",
                    "endDate": "2025-06-02"
                    # Missing roomTypeID and roomID
                }
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                
                if (response_data.get('success') is False and 
                    "roomTypeID or roomID must be specified" in response_data.get('message', '')):
                    self.log_test(
                        "Missing Room Assignment",
                        True,
                        f"Correctly rejected: {response_data.get('message')}"
                    )
                else:
                    self.log_test(
                        "Missing Room Assignment",
                        False,
                        f"Unexpected response: {response_data}",
                        response_data
                    )
            else:
                self.log_test(
                    "Missing Room Assignment",
                    False,
                    "Empty response"
                )
        except Exception as e:
            self.log_test(
                "Missing Room Assignment",
                False,
                f"Exception: {str(e)}"
            )
    
    async def run_all_tests(self):
        """Run all create_reservation tests."""
        print("🚀 Starting Create Reservation Testing...")
        print("=" * 60)
        
        try:
            async with Client(self.url) as client:
                # Test valid reservation creation
                reservation_id = await self.test_valid_reservation_creation(client)
                
                # Test validation scenarios
                await self.test_missing_required_fields(client)
                await self.test_date_validation(client)
                await self.test_room_assignment_validation(client)
                
                # Print summary
                self.print_summary()
                
                return reservation_id
                
        except Exception as e:
            print(f"❌ Error during testing: {str(e)}")
            return None
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "=" * 60)
        print("📊 CREATE RESERVATION TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests / total_tests * 100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n🔍 Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['message']}")

async def main():
    """Main test runner."""
    tester = CreateReservationTester()
    reservation_id = await tester.run_all_tests()
    
    if reservation_id:
        print(f"\n🎉 Successfully created test reservation: {reservation_id}")
    else:
        print(f"\n⚠️  No reservation was created during testing")

if __name__ == "__main__":
    asyncio.run(main())
