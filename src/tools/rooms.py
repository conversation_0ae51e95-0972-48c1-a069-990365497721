"""
Room tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds rooms and room types.
"""

from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_room_types(
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve room types from Cloudbeds API.

    Args:
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of room type data
    """
    return await api_request("getRoomTypes", property_id=property_id or CLOUDBEDS_PROPERTY_ID)

async def get_rooms(
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve rooms from Cloudbeds API.

    Args:
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of room data
    """
    return await api_request("getRooms", property_id=property_id or CLOUDBEDS_PROPERTY_ID)

async def get_room_availability(
    start_date: str,
    end_date: str,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve room availability for a date range.

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of availability data
    """
    params = {
        "dateFrom": start_date,
        "dateTo": end_date
    }
    
    return await api_request("getAvailability", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_room_rates(
    start_date: str,
    end_date: str,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve room rates for a date range.

    Args:
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of rate data
    """
    params = {
        "dateFrom": start_date,
        "dateTo": end_date
    }
    
    return await api_request("getRates", params, property_id or CLOUDBEDS_PROPERTY_ID)
