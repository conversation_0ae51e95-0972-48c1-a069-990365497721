"""
Test script for Cloudbeds MCP.

This script tests the FastMCP server for Cloudbeds API.
"""

import asyncio
from fastmcp import Client

async def test_cloudbeds_mcp():
    """Test the Cloudbeds MCP server."""
    print("Testing Cloudbeds MCP server...")
    
    # Connect to the MCP server
    async with <PERSON><PERSON>("main.py") as client:
        # Test getting room types
        print("\nTesting get_room_types_tool...")
        result = await client.call_tool("get_room_types_tool")
        print(f"Result: {result}")
        
        # Test getting recent reservations
        print("\nTesting recent_reservations_resource...")
        result = await client.get_resource("cloudbeds://reservations/recent")
        print(f"Result: {result}")
        
        # Test the summarize_reservation prompt
        print("\nTesting summarize_reservation prompt...")
        sample_reservation = {
            "reservationID": "12345",
            "guestName": "John Doe",
            "startDate": "2023-06-01",
            "endDate": "2023-06-05",
            "roomName": "Deluxe King",
            "status": "confirmed"
        }
        result = await client.call_prompt("summarize_reservation", {"reservation_data": sample_reservation})
        print(f"Result: {result}")

if __name__ == "__main__":
    asyncio.run(test_cloudbeds_mcp())
