"""
Test parameter isolation to find the problematic parameter.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_parameter_isolation():
    """Test parameters one by one to isolate the issue."""
    print("🔍 Testing Parameter Isolation...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Start with absolute minimal working parameters
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": 2,
        "children": 0
    }
    
    # Test 1: Base parameters without rooms
    print(f"\n📡 Test 1: Base parameters (should give 'Parameter rooms is required')")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=base_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if "rooms is required" in data.get('message', ''):
                    print(f"   ✅ Base parameters work correctly")
                else:
                    print(f"   ❌ Unexpected error: {data.get('message')}")
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 2: Add rooms parameter
    print(f"\n📡 Test 2: Add rooms parameter")
    try:
        test_data = base_data.copy()
        rooms = [{"roomTypeID": "653498", "adults": 2, "children": 0, "startDate": "2025-05-26", "endDate": "2025-05-27"}]
        test_data["rooms"] = json.dumps(rooms)
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ Rooms parameter works correctly")
                elif "Invalid Parameter Format" in data.get('message', ''):
                    print(f"   ❌ Rooms parameter causes Invalid Parameter Format")
                else:
                    print(f"   ⚠️  Different error: {data.get('message')}")
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 3: Add payment method
    print(f"\n📡 Test 3: Add payment method")
    try:
        test_data = base_data.copy()
        rooms = [{"roomTypeID": "653498", "adults": 2, "children": 0, "startDate": "2025-05-26", "endDate": "2025-05-27"}]
        test_data["rooms"] = json.dumps(rooms)
        test_data["paymentMethod"] = "credit"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reservation created!")
                    return True
                elif "Invalid Parameter Format" in data.get('message', ''):
                    print(f"   ❌ Payment method causes Invalid Parameter Format")
                else:
                    print(f"   ⚠️  Different error: {data.get('message')}")
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test 4: Try different payment methods
    payment_methods = ["cash", "credit", "visa", "master", "cards", "1", "2"]
    for payment_method in payment_methods:
        print(f"\n📡 Test 4.{payment_methods.index(payment_method)+1}: Payment method '{payment_method}'")
        try:
            test_data = base_data.copy()
            rooms = [{"roomTypeID": "653498", "adults": 2, "children": 0, "startDate": "2025-05-26", "endDate": "2025-05-27"}]
            test_data["rooms"] = json.dumps(rooms)
            test_data["paymentMethod"] = payment_method
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Payment method '{payment_method}' works!")
                        return True
                    elif "Invalid Parameter Format" not in data.get('message', ''):
                        print(f"   ✅ Payment method '{payment_method}' accepted")
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_parameter_isolation())
    if success:
        print(f"\n🎉 FOUND WORKING COMBINATION!")
    else:
        print(f"\n🔧 STILL NEED TO FIND THE RIGHT COMBINATION")
