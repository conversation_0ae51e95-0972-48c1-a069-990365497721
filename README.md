# Cloudbeds MCP (Model Control Panel)

A FastMCP-powered interface for the Cloudbeds Property Management System API, enabling AI assistants to interact with hotel management data.

## Purpose & Key Features

This project creates a Model Control Panel (MCP) for the Cloudbeds API, allowing AI assistants to:

- Retrieve and manage hotel reservations
- Access room and room type information
- Search and manage guest data
- Check room availability and rates
- Access property information and settings

The MCP provides structured tools, resources, and prompts that make it easy for AI systems to understand and work with Cloudbeds data.

## Prerequisites

- Python 3.12 or newer
- Cloudbeds API credentials (API key and property ID)
- [uv package manager](https://github.com/astral-sh/uv)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/cloudbeds-mcp-v3.git
   cd cloudbeds-mcp-v3
   ```

2. Create and activate a virtual environment using uv:
   ```bash
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install the project and its dependencies:
   ```bash
   uv pip install -e .
   ```

## Environment Setup

1. Create a `.env` file in the project root with your Cloudbeds API credentials:
   ```
   CLOUDBEDS_API_KEY=your_api_key_here
   CLOUDBEDS_PROPERTY_ID=your_property_id_here
   ```

2. Ensure the `.env` file is included in `.gitignore` to prevent committing sensitive information.

## Usage

### Starting the MCP Server

There are multiple ways to start the MCP server:

#### Option 1: Using FastMCP CLI (for development with inspector)

```bash
fastmcp dev main.py
```

This will start the server with the MCP Inspector interface for interactive testing.

#### Option 2: Using main.py directly

```bash
python main.py --transport streamable-http --host localhost --port 8000 --path /mcp
```

This script accepts command-line arguments to customize the transport type, host, port, and path.

#### Option 3: Using start_mcp.py (simplified)

```bash
python start_mcp.py
```

This is a simplified script specifically for starting the MCP with HTTP transport on localhost:8000/mcp.

### Difference Between main.py and start_mcp.py

- **main.py**:
  - More flexible script that accepts command-line arguments
  - Allows different transport types (stdio, streamable-http, sse)
  - Lets you customize host, port, and path
  - Default transport is "stdio" if no arguments are provided

- **start_mcp.py**:
  - Simplified script specifically for starting the MCP with HTTP transport
  - Has hardcoded settings for streamable-http transport
  - Always uses localhost:8000/mcp
  - No command-line arguments needed

**Recommendation**: Use `start_mcp.py` for simplicity when connecting to Cursor IDE or other clients that require HTTP transport. Use `main.py` when you need more flexibility or different transport options.

### Testing with the Client

You can test the MCP functionality using the included test script:

```bash
python test_mcp_connection.py
```

### Connecting to Cursor IDE

To use the Cloudbeds MCP with Cursor IDE:

1. **Start the MCP server**:
   ```bash
   python start_mcp.py
   ```

2. **Verify the server is running**:
   ```bash
   python test_mcp_connection.py
   ```

3. **Configure Cursor IDE**:
   - The project includes a `.cursor/mcp.json` file that configures Cursor to connect to the MCP server
   - In Cursor IDE, go to Settings > MCP Servers
   - You should see "cloudbeds-mcp" in the list of available servers
   - Make sure it's enabled (toggle switch is on)

4. **Use the MCP in Cursor IDE**:
   - Ask the AI agent questions about hotel reservations, rooms, or guests
   - Example: "Show me the available room types"

For more detailed instructions, see the [MCP_SETUP.md](MCP_SETUP.md) file.

### Backing Up Data

To create a comprehensive backup of your Cloudbeds data:

```bash
python utils/backup_from_api.py --days 60 --output-dir cloudbeds_backup
```

## Available Tools

The MCP provides the following tools for AI assistants:

### Reservation Tools
- `get_reservations_tool`: Retrieve reservations from the past X days
- `get_reservation_tool`: Get detailed information for a specific reservation
- `get_invoice_tool`: Retrieve invoice information for a reservation
- `create_reservation_tool`: Create a new reservation

### Room Tools
- `get_room_types_tool`: Retrieve all room types
- `get_rooms_tool`: Get all rooms
- `get_availability_tool`: Check room availability for a date range

### Guest Tools
- `search_guests_tool`: Search for guests by name, email, or phone
- `get_guest_tool`: Get detailed information for a specific guest

## Available Resources

Resources provide structured access to Cloudbeds data:

- `cloudbeds://reservations/recent`: Recent reservations
- `cloudbeds://reservations/{reservation_id}`: Specific reservation details
- `cloudbeds://rooms/types`: All room types
- `cloudbeds://rooms`: All rooms
- `cloudbeds://availability/next30days`: Availability for the next 30 days
- `cloudbeds://property`: Property information

## Testing

Run the test script to verify that the MCP is working correctly:

```bash
python test_mcp.py
```

This will test various tools and resources and display the results.

## Troubleshooting

### API Authentication Issues
- Verify your API key and property ID in the `.env` file
- Check that the `.env` file is in the project root directory
- Ensure the API key has the necessary permissions

### MCP Server Won't Start
- Check that all dependencies are installed: `uv pip list`
- Verify Python version is 3.12 or newer: `python --version`
- Check for error messages in the console output

### Rate Limiting
- The Cloudbeds API has rate limits. If you encounter errors, try reducing the frequency of requests
- The client includes built-in rate limiting (5 requests per second)

## Project Structure

```
cloudbeds-mcp-v3/
├── .cursor/                # Cursor IDE configuration
│   └── mcp.json            # MCP configuration for Cursor IDE
├── .env                    # Environment variables (create this)
├── .env.example            # Example environment file
├── main.py                 # Entry point for the MCP server (with CLI args)
├── start_mcp.py            # Simplified script to start MCP with HTTP transport
├── test_mcp_connection.py  # Script to test MCP connection
├── MCP_SETUP.md            # Detailed instructions for Cursor IDE setup
├── pyproject.toml          # Project metadata and dependencies
├── utils/
│   └── backup_from_api.py  # Script to backup Cloudbeds data
├── src/
│   ├── server.py           # MCP server configuration
│   ├── config.py           # Configuration settings
│   ├── cloudbeds_client.py # Cloudbeds API client
│   ├── tools/              # Tool implementations
│   │   ├── reservations.py # Reservation-related tools
│   │   ├── rooms.py        # Room-related tools
│   │   └── guests.py       # Guest-related tools
│   ├── resources/          # Resource implementations
│   │   ├── reservations.py # Reservation resources
│   │   ├── rooms.py        # Room resources
│   │   └── property.py     # Property resources
│   └── prompts/            # Prompt templates
│       └── reservation_prompts.py # Reservation-related prompts
```