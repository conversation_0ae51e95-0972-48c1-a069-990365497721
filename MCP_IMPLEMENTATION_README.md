# MCP Server for Cloudbeds Reservations - Production Implementation

## Overview

This is a production-ready MCP (Model Context Protocol) server that integrates with the Cloudbeds API for hotel reservation management. The implementation has been thoroughly tested and optimized for reliability and performance.

## Architecture

The MCP server is built using FastMCP and provides tools for:
- Creating hotel reservations with full validation
- Retrieving reservation details
- Managing guest information
- Handling room assignments with proper FormData structure

## Key Components

### 1. MCP Server (`src/server.py`)
- FastMCP-based server implementation
- HTTP transport on port 8000
- Tool registration and request handling
- Production-ready error handling

### 2. Cloudbeds API Client (`src/cloudbeds_client.py`)
- Async HTTP client using httpx
- x-api-key authentication with X-PROPERTY-ID headers
- Rate limiting (10 requests/second)
- FormData support for reservation creation
- Comprehensive error handling

### 3. Reservation Tools (`src/tools/reservations.py`)
- `create_reservation_tool`: Creates new reservations with proper FormData structure
- `get_reservation_tool`: Retrieves reservation details
- Complete input validation and error handling
- Support for indexed room parameters (rooms[i][field] format)

### 4. Configuration (`src/config.py`)
- Environment variable management
- API endpoints and credentials
- Rate limiting configuration

## Installation and Setup

### Prerequisites
- Python 3.8+
- Cloudbeds API credentials
- Valid Cloudbeds property ID

### Environment Variables
Create a `.env` file with the following variables:

```env
CLOUDBEDS_API_KEY=your_api_key_here
CLOUDBEDS_PROPERTY_ID=your_property_id_here
```

### Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Start the MCP server
python start_mcp.py
```

## API Implementation Details

### Reservation Creation Structure

The implementation uses a specific FormData structure that matches the Cloudbeds API requirements:

```python
# Guest data (flat parameters)
"guestFirstName": "John"
"guestLastName": "Doe"
"guestEmail": "<EMAIL>"
"guestCountry": "ES"

# Room data (indexed format)
"rooms[0][roomTypeID]": "653498"
"rooms[0][quantity]": "1"
"adults[0][roomTypeID]": "653498"
"adults[0][quantity]": "2"
"children[0][roomTypeID]": "653498"
"children[0][quantity]": "0"

# Required parameters
"paymentMethod": "credit" or "noPayment"
"sourceID": "s-2-1" (formatted as s-{id}-1)
"startDate": "2025-05-26"
"endDate": "2025-05-27"
```

### Key Implementation Features

1. **Proper sourceID Formatting**: Automatically formats sourceID to `s-{id}-1` format
2. **FormData Structure**: Uses indexed room parameters for proper API compatibility
3. **Payment Method Support**: Supports both 'credit' and 'noPayment' payment methods
4. **Comprehensive Validation**: Validates all input data before API calls
5. **Error Handling**: Provides clear error messages for debugging

## Usage Examples

### Creating a Reservation

```python
reservation_data = {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "country": "ES",
    "roomTypeID": "653498",
    "startDate": "2025-05-26",
    "endDate": "2025-05-27",
    "adults": 2,
    "children": 0,
    "paymentMethod": "credit"
}

# Call via MCP
result = await client.call_tool("create_reservation_tool", {
    "reservation_data": reservation_data
})
```

### Response Format

```json
{
    "success": true,
    "reservationID": "2729688851475",
    "confirmationCode": null,
    "message": "Reservation created successfully",
    "thirdPartyIdentifier": "mcp-1748176704"
}
```

## Production Considerations

### Security
- API keys are stored in environment variables
- No sensitive data is logged
- Proper error handling prevents information leakage

### Performance
- Rate limiting respects Cloudbeds API limits (10 req/sec)
- Async implementation for better concurrency
- Efficient FormData processing

### Reliability
- Comprehensive input validation
- Proper error handling and recovery
- Detailed logging for debugging

### Monitoring
- Error logging for production monitoring
- Request/response tracking
- Performance metrics available

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Verify API key and property ID
2. **Invalid Parameter Format**: Check that all required fields are provided
3. **Rate Limiting**: Ensure requests don't exceed 10/second

### Error Codes

- `400`: Invalid request parameters
- `401`: Authentication failed
- `403`: Insufficient permissions
- `429`: Rate limit exceeded

## Testing

The implementation has been thoroughly tested with:
- Valid reservation creation scenarios
- Error handling edge cases
- API rate limiting
- FormData structure validation

## Support

For issues or questions:
1. Check the error logs in `logs/mcp_server.log`
2. Verify environment variables are set correctly
3. Ensure Cloudbeds API credentials are valid
4. Check network connectivity to Cloudbeds API

## Version History

- **v1.0**: Production-ready implementation with FormData support
- **v0.9**: Beta implementation with JSON structure
- **v0.8**: Initial development version
