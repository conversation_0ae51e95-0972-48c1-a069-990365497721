"""
Check detailed reservation structure.
"""

import asyncio
import json
from fastmcp import Client

async def check_reservation_details():
    """Check detailed structure of existing reservations."""
    print("🔍 Checking Detailed Reservation Structure...")
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Get a specific reservation details
        print("\n📋 Detailed Reservation Structure:")
        try:
            # Use the reservation ID we found
            reservation_id = "5113613993769"
            details = await client.call_tool("get_reservation_tool", {"reservation_id": reservation_id})
            if details and details[0].text:
                detail_data = json.loads(details[0].text)
                if detail_data:
                    print(f"   Reservation ID: {detail_data.get('reservationID')}")
                    print(f"   Property ID: {detail_data.get('propertyID')}")
                    print(f"   Guest Name: {detail_data.get('guestName')}")
                    print(f"   Start Date: {detail_data.get('startDate')}")
                    print(f"   End Date: {detail_data.get('endDate')}")
                    print(f"   Adults: {detail_data.get('adults')}")
                    print(f"   Children: {detail_data.get('children')}")
                    print(f"   Source ID: {detail_data.get('sourceID')}")
                    print(f"   Source Name: {detail_data.get('sourceName')}")
                    print(f"   Status: {detail_data.get('status')}")
                    
                    # Look for room-related fields
                    print(f"\n   🏨 Room Information:")
                    print(f"   Room Type ID: {detail_data.get('roomTypeID', 'Not found')}")
                    print(f"   Room ID: {detail_data.get('roomID', 'Not found')}")
                    print(f"   Room Name: {detail_data.get('roomName', 'Not found')}")
                    print(f"   Room Type Name: {detail_data.get('roomTypeName', 'Not found')}")
                    
                    # Show all keys
                    print(f"\n   📋 All available keys:")
                    for key in sorted(detail_data.keys()):
                        value = detail_data[key]
                        if isinstance(value, (dict, list)):
                            print(f"     {key}: {type(value).__name__} (length: {len(value)})")
                        else:
                            print(f"     {key}: {value}")
                else:
                    print(f"   ⚠️  No detailed data returned")
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_reservation_details())
