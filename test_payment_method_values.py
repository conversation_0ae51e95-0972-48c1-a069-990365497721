"""
Test different payment method values to find the correct one.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_payment_method_values():
    """Test different payment method values."""
    print("💳 Testing Different Payment Method Values...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base working parameters
    base_data = {
        "propertyID": "317353",
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": f"payment.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])
    }
    
    # Test different payment method values
    payment_methods = [
        "cash",
        "CASH", 
        "Cash",
        "credit_card",
        "creditcard",
        "credit-card",
        "Credit Card",
        "visa",
        "VISA",
        "Visa",
        "mastercard",
        "MasterCard",
        "amex",
        "american_express",
        "paypal",
        "PayPal",
        "bank_transfer",
        "wire_transfer",
        "check",
        "cheque",
        "none",
        "None",
        "direct",
        "Direct",
        "online",
        "Online",
        "deposit",
        "Deposit",
        "1",  # Numeric IDs
        "2",
        "3",
        "4",
        "5"
    ]
    
    for payment_method in payment_methods:
        print(f"\n📡 Testing paymentMethod: '{payment_method}'")
        try:
            test_data = base_data.copy()
            test_data["paymentMethod"] = payment_method
            test_data["guestEmail"] = f"payment.{payment_method.replace(' ', '').replace('_', '').lower()}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Payment method '{payment_method}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        print(f"   Full response: {json.dumps(data, indent=2)}")
                        return payment_method
                    elif "Invalid Parameter Format" not in data.get('message', ''):
                        print(f"   ✅ Payment method '{payment_method}' accepted, next error: {data.get('message')}")
                        # Continue to see if we can find one that works completely
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No valid payment method found")
    return None

if __name__ == "__main__":
    asyncio.run(test_payment_method_values())
