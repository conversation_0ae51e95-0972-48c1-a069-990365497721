"""
Test with valid country to see if that resolves the issue.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_with_country():
    """Test with valid country."""
    print("🔍 Test with Valid Country")
    print("=" * 50)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Test 1: With country ES
        print(f"\n📋 Test 1: With country='ES'")
        reservation_es = {
            "firstName": "Test",
            "lastName": "User",
            "email": f"country.es.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "country": "ES",  # Add valid country
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0
        }
        
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_es
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"📊 Response: {response_data.get('message')}")
                
                if response_data.get('success'):
                    print(f"🎉 SUCCESS with ES!")
                    return True
                elif "paymentMethod is required" in response_data.get('message', ''):
                    print(f"✅ Progress! Now we need payment method")
                    
                    # Test 2: Add payment method
                    print(f"\n📋 Test 2: Adding paymentMethod='noPayment'")
                    reservation_es['paymentMethod'] = 'noPayment'
                    
                    result2 = await client.call_tool("create_reservation_tool", {
                        "reservation_data": reservation_es
                    })
                    
                    if result2 and result2[0].text:
                        response_data2 = json.loads(result2[0].text)
                        print(f"📊 Response: {response_data2.get('message')}")
                        
                        if response_data2.get('success'):
                            print(f"🎉 SUCCESS with noPayment!")
                            return True
                        else:
                            print(f"❌ Still failed: {response_data2.get('message')}")
                else:
                    print(f"❌ Different error: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"💥 Exception: {str(e)}")
        
        # Test 3: Try with FR (we know this worked before)
        print(f"\n📋 Test 3: With country='FR'")
        reservation_fr = {
            "firstName": "Test",
            "lastName": "User", 
            "email": f"country.fr.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "country": "FR",  # Try FR
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0
        }
        
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_fr
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"📊 Response: {response_data.get('message')}")
                
                if response_data.get('success'):
                    print(f"🎉 SUCCESS with FR!")
                    return True
                elif "paymentMethod is required" in response_data.get('message', ''):
                    print(f"✅ Progress! FR works, now need payment method")
                else:
                    print(f"❌ Different error with FR: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"💥 Exception with FR: {str(e)}")
        
        # Test 4: Try without country (should use default)
        print(f"\n📋 Test 4: Without country (should default)")
        reservation_no_country = {
            "firstName": "Test",
            "lastName": "User",
            "email": f"no.country.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0
        }
        
        try:
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_no_country
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"📊 Response: {response_data.get('message')}")
                
                if response_data.get('success'):
                    print(f"🎉 SUCCESS without country!")
                    return True
                elif "paymentMethod is required" in response_data.get('message', ''):
                    print(f"✅ Progress! Default country works")
                else:
                    print(f"❌ Error without country: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"💥 Exception without country: {str(e)}")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_with_country())
    if success:
        print(f"\n🎉 COUNTRY ISSUE RESOLVED!")
    else:
        print(f"\n🔧 STILL DEBUGGING COUNTRY ISSUE...")
