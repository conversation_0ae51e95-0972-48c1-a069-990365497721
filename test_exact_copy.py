"""
Test with exact copy of existing reservation parameters.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_exact_copy():
    """Test with exact copy of existing reservation parameters."""
    print("📋 Testing with Exact Copy of Existing Reservation...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Use exact parameters from existing reservation analysis
    # From existing reservation: Garden Junior (653498), FR country, s-1 source
    print("\n📡 Test: Exact copy with all known parameters")
    try:
        exact_data = {
            "propertyID": "317353",  # Exact property ID
            "guestFirstName": "Test",  # Same as existing
            "guestLastName": "User",   # Same as existing
            "guestEmail": f"exact.copy.{int(asyncio.get_event_loop().time())}@example.com",
            "guestCountry": "FR",      # Same as existing
            "guestPhone": "+1234567890",  # Same format as existing
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498",    # Garden Junior - same as existing
            "adults": "2",             # Same as existing
            "children": "0",           # Same as existing
            "sourceID": "s-1",         # Same as existing (Website/Booking Engine)
            "paymentMethod": "Cash",
            "rooms": json.dumps([{"roomTypeID": "653498", "adults": "2", "children": "0"}])
        }
        
        print(f"   Exact data: {exact_data}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=exact_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Exact copy works!")
                    if 'reservationID' in data:
                        print(f"   Reservation ID: {data['reservationID']}")
                    return True
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Try without some optional parameters
    print("\n📡 Test: Reduced parameters")
    try:
        reduced_data = {
            "propertyID": "317353",
            "guestFirstName": "Test",
            "guestLastName": "User",
            "guestEmail": f"reduced.{int(asyncio.get_event_loop().time())}@example.com",
            "guestCountry": "FR",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498",
            "adults": "2",
            "children": "0",
            "rooms": "653498"  # Try simple format
        }
        
        print(f"   Reduced data: {reduced_data}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=reduced_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Reduced parameters work!")
                    return True
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    asyncio.run(test_exact_copy())
