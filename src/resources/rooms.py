"""
Room resources for Cloudbeds MCP.

This module provides resources for accessing Cloudbeds room data.
"""

from typing import Dict, List, Optional, Any
import datetime

from src.tools.rooms import get_room_types, get_rooms, get_room_availability

async def get_all_room_types() -> List[Dict[str, Any]]:
    """
    Get all room types as a resource.
    
    Returns:
        List[Dict[str, Any]]: List of room type data
    """
    return await get_room_types()

async def get_all_rooms() -> List[Dict[str, Any]]:
    """
    Get all rooms as a resource.
    
    Returns:
        List[Dict[str, Any]]: List of room data
    """
    return await get_rooms()

async def get_availability_next_30_days() -> List[Dict[str, Any]]:
    """
    Get room availability for the next 30 days.
    
    Returns:
        List[Dict[str, Any]]: List of availability data
    """
    today = datetime.datetime.now()
    end_date = today + datetime.timedelta(days=30)
    
    start_date_str = today.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    return await get_room_availability(start_date_str, end_date_str)
