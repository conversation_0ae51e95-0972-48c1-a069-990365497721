"""
Reservation tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds reservations.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_reservations(
    days_back: int = 30,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation data
    """
    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    return await api_request("getReservations", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_details(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed reservation data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getReservation", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_invoice(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Invoice data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getInvoice", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_reservation(
    reservation_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds using the proven working implementation pattern.

    Based on the codebase analysis, this function uses the correct API structure with
    nested guestData and roomsData objects, proper sourceID formatting, and FormData
    for maximum compatibility with the Cloudbeds API.

    Args:
        reservation_data (dict): Reservation data containing:
            - Guest information: firstName, lastName, email, phone (optional), address, city, country, postalCode
            - Room information: roomTypeID or roomName, startDate, endDate, adults, children
            - Payment: paymentMethod ('credit' for credit card, 'noPayment' for no immediate payment)
            - Optional: status, sendEmailConfirmation, sourceID, thirdPartyIdentifier, notes
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created reservation data or error information
    """
    # Validate required fields
    required_fields = ['firstName', 'lastName', 'email', 'startDate', 'endDate']
    missing_fields = [field for field in required_fields if not reservation_data.get(field)]

    if missing_fields:
        return {
            "success": False,
            "message": f"Missing required fields: {', '.join(missing_fields)}"
        }

    # Validate date format
    try:
        start_date = datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')

        if end_date <= start_date:
            return {
                "success": False,
                "message": "End date must be after start date"
            }
    except ValueError as e:
        return {
            "success": False,
            "message": f"Invalid date format. Use YYYY-MM-DD: {str(e)}"
        }

    # Validate room assignment - must have either roomTypeID or roomName
    if not reservation_data.get('roomTypeID') and not reservation_data.get('roomName'):
        return {
            "success": False,
            "message": "Either roomTypeID or roomName must be specified"
        }

    # Prepare the request data using the EXACT working implementation structure
    # Based on codebase analysis: nested structure converted to FormData

    # Generate unique third-party identifier if not provided
    import time
    third_party_id = reservation_data.get('thirdPartyIdentifier', f"mcp-{int(time.time())}")

    # Prepare guest data object (nested structure as per working implementation)
    guest_data = {
        "firstName": reservation_data['firstName'],
        "lastName": reservation_data['lastName'],
        "email": reservation_data['email'],
        "country": reservation_data.get('country', 'ES')  # Always include country
    }

    # Add optional guest fields
    if reservation_data.get('phone'):
        guest_data['phone'] = reservation_data['phone']
    if reservation_data.get('address'):
        guest_data['address'] = reservation_data['address']
    if reservation_data.get('city'):
        guest_data['city'] = reservation_data['city']
    if reservation_data.get('postalCode'):
        guest_data['postalCode'] = reservation_data['postalCode']
    if reservation_data.get('notes'):
        guest_data['notes'] = reservation_data['notes']

    # Prepare room data array (nested structure as per working implementation)
    room_data = {
        "startDate": reservation_data['startDate'],
        "endDate": reservation_data['endDate'],
        "adults": reservation_data.get('adults', 1),
        "children": reservation_data.get('children', 0)
    }

    # Add room type ID or name
    if reservation_data.get('roomTypeID'):
        room_data['roomTypeID'] = str(reservation_data['roomTypeID'])
    elif reservation_data.get('roomName'):
        room_data['roomName'] = reservation_data['roomName']

    # Add optional room fields
    if reservation_data.get('ratePlanID'):
        room_data['ratePlanID'] = str(reservation_data['ratePlanID'])
    if reservation_data.get('roomNotes'):
        room_data['notes'] = reservation_data['roomNotes']

    # Format sourceID correctly as per codebase analysis: s-{id}-1
    source_id = reservation_data.get('sourceID', '2')  # Default to 2 (Web)
    if isinstance(source_id, str) and source_id.startswith('s-'):
        formatted_source_id = source_id
    else:
        # Remove any existing format and apply correct format
        clean_id = str(source_id).replace('s-', '').replace('-1', '')
        formatted_source_id = f"s-{clean_id}-1"

    # Create FormData structure as per working implementation
    # Based on codebase analysis: use specific FormData format for rooms
    api_data = {
        "propertyID": property_id or CLOUDBEDS_PROPERTY_ID,

        # Global dates (required at top level)
        "startDate": reservation_data['startDate'],
        "endDate": reservation_data['endDate'],

        # Guest data as flat parameters
        "guestFirstName": guest_data['firstName'],
        "guestLastName": guest_data['lastName'],
        "guestEmail": guest_data['email'],
        "guestCountry": guest_data['country'],

        # Status and identifiers
        "status": reservation_data.get('status', 'confirmed'),
        "thirdPartyIdentifier": third_party_id,
        "sendEmailConfirmation": str(reservation_data.get('sendEmailConfirmation', True)).lower(),
        "sourceID": formatted_source_id,

        # Payment method (required)
        "paymentMethod": reservation_data.get('paymentMethod', 'credit')
    }

    # Add optional guest fields
    if guest_data.get('phone'):
        api_data['guestPhone'] = guest_data['phone']
    if guest_data.get('address'):
        api_data['guestAddress'] = guest_data['address']
    if guest_data.get('city'):
        api_data['guestCity'] = guest_data['city']
    if guest_data.get('postalCode'):
        api_data['guestPostalCode'] = guest_data['postalCode']
    if guest_data.get('notes'):
        api_data['guestNotes'] = guest_data['notes']

    # Add room data using the working implementation format
    # Based on codebase: rooms[i][field] format for multiple rooms
    room_index = 0

    # Room type and quantity
    if room_data.get('roomTypeID'):
        api_data[f'rooms[{room_index}][roomTypeID]'] = room_data['roomTypeID']
        api_data[f'rooms[{room_index}][quantity]'] = '1'
    elif room_data.get('roomName'):
        api_data[f'rooms[{room_index}][roomName]'] = room_data['roomName']
        api_data[f'rooms[{room_index}][quantity]'] = '1'

    # Adults and children with room type association
    api_data[f'adults[{room_index}][roomTypeID]'] = room_data.get('roomTypeID', room_data.get('roomName', ''))
    api_data[f'adults[{room_index}][quantity]'] = str(room_data['adults'])
    api_data[f'children[{room_index}][roomTypeID]'] = room_data.get('roomTypeID', room_data.get('roomName', ''))
    api_data[f'children[{room_index}][quantity]'] = str(room_data['children'])

    # Add optional room fields
    if room_data.get('ratePlanID'):
        api_data[f'rooms[{room_index}][ratePlanID]'] = room_data['ratePlanID']
    if room_data.get('notes'):
        api_data[f'rooms[{room_index}][notes]'] = room_data['notes']

    # Make the API request using the direct method (FormData approach as per working implementation)
    try:
        result = await api_request(
            "postReservation",
            data=api_data,
            property_id=property_id or CLOUDBEDS_PROPERTY_ID,
            method="POST"
        )

        # Handle the response according to working implementation patterns
        if isinstance(result, dict):
            if result.get('success'):
                return {
                    "success": True,
                    "reservationID": result.get("reservationID") or result.get("data", {}).get("reservationID"),
                    "confirmationCode": result.get("confirmationCode") or result.get("data", {}).get("confirmationCode"),
                    "message": "Reservation created successfully",
                    "thirdPartyIdentifier": third_party_id
                }
            else:
                return {
                    "success": False,
                    "message": result.get("message", "Failed to create reservation"),
                    "error": result.get("error")
                }
        else:
            return result

    except Exception as e:
        return {
            "success": False,
            "message": f"Error creating reservation: {str(e)}"
        }

async def update_reservation(
    reservation_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (dict): Data to update
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data
    """
    # This would need to be implemented with a PUT request
    # For now, we'll return a placeholder
    return {"message": "Reservation update not implemented yet"}
