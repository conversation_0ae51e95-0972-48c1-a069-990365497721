"""
Test with the correct paymentMethod values: 'credit' and 'noPayment'
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_final_payment_methods():
    """Test with the correct payment method values."""
    print("🎯 Testing Final Payment Method Values")
    print("=" * 70)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Test 1: With 'noPayment'
        print(f"\n📋 Test 1: Using paymentMethod='noPayment'")
        
        reservation_no_payment = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"final.nopayment.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34123456789",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0,
            "paymentMethod": "noPayment"  # Correct value!
        }
        
        try:
            print(f"\n🚀 Creating reservation with paymentMethod='noPayment'...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_no_payment
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created with 'noPayment'!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    
                    # Verify the reservation
                    if response_data.get('reservationID'):
                        print(f"\n🔍 Verifying reservation...")
                        try:
                            verify_result = await client.call_tool("get_reservation_tool", {
                                "reservation_id": response_data['reservationID']
                            })
                            if verify_result and verify_result[0].text:
                                verify_data = json.loads(verify_result[0].text)
                                if verify_data.get('success') is not False:
                                    print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                                else:
                                    print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                        except Exception as ve:
                            print(f"   ⚠️  Verification error: {ve}")
                    
                    return True
                else:
                    print(f"\n❌ Failed with 'noPayment': {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with 'noPayment': {str(e)}")
        
        # Test 2: With 'credit'
        print(f"\n" + "=" * 70)
        print(f"📋 Test 2: Using paymentMethod='credit'")
        
        reservation_credit = {
            "firstName": "Jane",
            "lastName": "Smith",
            "email": f"final.credit.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34987654321",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 1,
            "children": 0,
            "paymentMethod": "credit"  # Correct value!
        }
        
        try:
            print(f"\n🚀 Creating reservation with paymentMethod='credit'...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": reservation_credit
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Reservation created with 'credit'!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    return True
                else:
                    print(f"\n❌ Failed with 'credit': {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with 'credit': {str(e)}")
        
        # Test 3: Complete reservation with all details
        print(f"\n" + "=" * 70)
        print(f"📋 Test 3: Complete reservation with all details")
        
        complete_reservation = {
            "firstName": "Carlos",
            "lastName": "Rodriguez",
            "email": f"complete.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34666777888",
            "address": "Calle Gran Via 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 1,
            "status": "confirmed",
            "sendEmailConfirmation": True,
            "sourceID": "s-2-1",
            "paymentMethod": "noPayment",
            "thirdPartyIdentifier": f"mcp-complete-{int(datetime.now().timestamp())}",
            "notes": "Test reservation with complete data"
        }
        
        try:
            print(f"\n🚀 Creating complete reservation...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": complete_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Complete reservation created!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    print(f"   Third Party ID: {response_data.get('thirdPartyIdentifier')}")
                    return True
                else:
                    print(f"\n❌ Failed complete reservation: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with complete reservation: {str(e)}")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_final_payment_methods())
    if success:
        print(f"\n🎉🎉🎉 PAYMENT METHOD ISSUE RESOLVED! 🎉🎉🎉")
        print(f"🚀 RESERVATION CREATION IS NOW FULLY WORKING!")
        print(f"✅ MCP SERVER IS PRODUCTION READY!")
    else:
        print(f"\n🔧 STILL INVESTIGATING PAYMENT METHOD ISSUE")
