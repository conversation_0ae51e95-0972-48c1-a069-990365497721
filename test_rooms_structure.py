"""
Test different rooms JSON structures.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_rooms_structure():
    """Test different rooms JSON structures."""
    print("🏨 Testing Different Rooms JSON Structures...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "John",
        "guestLastName": "Doe",
        "guestEmail": f"rooms.test.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "FR",  # Use valid country
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "paymentMethod": "Cash"
    }
    
    # Test different rooms structures
    rooms_structures = [
        # Test 1: Just roomTypeID
        [{"roomTypeID": "653498"}],
        
        # Test 2: roomTypeID with adults/children
        [{"roomTypeID": "653498", "adults": "2", "children": "0"}],
        
        # Test 3: roomTypeID with numeric adults/children
        [{"roomTypeID": "653498", "adults": 2, "children": 0}],
        
        # Test 4: Multiple fields
        [{"roomTypeID": "653498", "adults": "2", "children": "0", "nights": "1"}],
        
        # Test 5: Empty object
        [{}],
        
        # Test 6: Just the room type as string
        ["653498"],
        
        # Test 7: Object with different field names
        [{"roomType": "653498", "adultCount": "2", "childCount": "0"}]
    ]
    
    for i, rooms_structure in enumerate(rooms_structures, 1):
        print(f"\n📡 Test {i}: Rooms structure: {rooms_structure}")
        try:
            test_data = base_data.copy()
            test_data["rooms"] = json.dumps(rooms_structure)
            test_data["guestEmail"] = f"rooms.test{i}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Rooms structure {i} works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        print(f"   Full response: {json.dumps(data, indent=2)}")
                        return rooms_structure
                    elif "Invalid Parameter Format" not in data.get('message', ''):
                        print(f"   ✅ Rooms structure accepted, other issue: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No valid rooms structure found")
    return None

if __name__ == "__main__":
    asyncio.run(test_rooms_structure())
