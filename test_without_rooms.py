"""
Test without rooms parameter to see what other errors we get.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_without_rooms():
    """Test without rooms parameter."""
    print("🔍 Testing Without Rooms Parameter...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Test with all parameters except rooms
    print("\n📡 Test: All parameters except rooms")
    try:
        test_data = {
            "propertyID": CLOUDBEDS_PROPERTY_ID,
            "guestFirstName": "<PERSON>",
            "guestLastName": "Doe",
            "guestEmail": f"norooms.{int(asyncio.get_event_loop().time())}@example.com",
            "guestCountry": "FR",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498",
            "adults": "2",
            "children": "0",
            "paymentMethod": "Cash"
        }
        
        print(f"   Data: {test_data}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! No rooms parameter needed!")
                    return True
                else:
                    print(f"   Expected 'Parameter rooms is required', got: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    # Test with minimal parameters
    print("\n📡 Test: Absolute minimal parameters")
    try:
        minimal_data = {
            "propertyID": CLOUDBEDS_PROPERTY_ID,
            "guestFirstName": "John",
            "guestLastName": "Doe",
            "guestEmail": f"minimal.{int(asyncio.get_event_loop().time())}@example.com",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "roomTypeID": "653498"
        }
        
        print(f"   Minimal data: {minimal_data}")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{API_BASE_URL}/postReservation", data=minimal_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 SUCCESS! Minimal parameters work!")
                    return True
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    asyncio.run(test_without_rooms())
