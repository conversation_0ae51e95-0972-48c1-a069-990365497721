# Technical README: MCP Server for Reservation Creation

## Overview
This document provides comprehensive technical information for creating an MCP (Model Context Protocol) server that can create reservations based on the existing codebase. The system integrates with Cloudbeds API and manages reservations through a Supabase database.

## System Architecture

### Core Components
1. **Cloudbeds API Integration** - External hotel management system
2. **Supabase Database** - PostgreSQL database with Row Level Security
3. **SvelteKit Backend** - API endpoints and business logic
4. **TypeScript Types** - Comprehensive type definitions

## Database Schema

### Reservations Table Structure
Based on the TypeScript types and service implementations, the reservations table has the following structure:

```sql
-- Core reservations table (inferred from codebase)
CREATE TABLE public.reservations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_id UUID NOT NULL REFERENCES public.sites(id),
  suite_id UUID NOT NULL REFERENCES public.suites(id),
  user_id UUID REFERENCES auth.users(id),
  cloudbeds_reservation_id TEXT,
  cloudbeds_confirmation_code TEXT,
  guest_name TEXT NOT NULL,
  guest_email TEXT NOT NULL,
  guest_phone TEXT,
  check_in_date DATE NOT NULL,
  check_out_date DATE NOT NULL,
  adults INTEGER NOT NULL DEFAULT 1,
  children INTEGER NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'confirmed',
  total_price DECIMAL(10,2),
  currency TEXT DEFAULT 'EUR',
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Access tokens for reservation viewing
CREATE TABLE public.reservation_access_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  reservation_id UUID NOT NULL REFERENCES public.reservations(id),
  token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- View for reservations with related data
CREATE VIEW public.reservations_with_details AS
SELECT
  r.*,
  s.name AS suite_name,
  s.slug AS suite_slug,
  st.name AS site_name,
  st.domain AS site_domain,
  c.name AS client_name
FROM public.reservations r
JOIN public.suites s ON r.suite_id = s.id
JOIN public.sites st ON r.site_id = st.id
LEFT JOIN public.clients c ON st.client_id = c.id;
```

### Related Tables
```sql
-- Sites table
CREATE TABLE public.sites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES public.clients(id),
  name TEXT NOT NULL,
  domain TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Suites table
CREATE TABLE public.suites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  site_id UUID NOT NULL REFERENCES public.sites(id),
  name TEXT NOT NULL,
  slug TEXT NOT NULL,
  description TEXT,
  images JSONB DEFAULT '[]'::jsonb,
  videos JSONB DEFAULT '[]'::jsonb,
  features JSONB DEFAULT '[]'::jsonb,
  max_occupancy INTEGER DEFAULT 2,
  base_price DECIMAL(10,2),
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Cloudbeds mapping for suites
CREATE TABLE public.suite_cloudbeds_mapping (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  suite_id UUID NOT NULL REFERENCES public.suites(id),
  cloudbeds_room_type_id TEXT NOT NULL,
  cloudbeds_property_id TEXT NOT NULL,
  sync_images BOOLEAN DEFAULT false,
  sync_description BOOLEAN DEFAULT false,
  sync_amenities BOOLEAN DEFAULT false,
  sync_pricing BOOLEAN DEFAULT false,
  sync_availability BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## TypeScript Types

### Core Reservation Types
```typescript
// Main reservation type
export type Reservation = {
  id: string;
  site_id: string;
  suite_id: string;
  user_id?: string;
  cloudbeds_reservation_id?: string;
  cloudbeds_confirmation_code?: string;
  guest_name: string;
  guest_email: string;
  guest_phone?: string;
  check_in_date: string;
  check_out_date: string;
  adults: number;
  children: number;
  status: string;
  total_price?: number;
  currency?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  suite_name?: string;
  suite_slug?: string;
  site_name?: string;
  site_domain?: string;
  client_name?: string;
};
```

### Cloudbeds API Types
```typescript
// Guest information
export interface CloudbedsReservationGuest {
  readonly firstName: string;
  readonly lastName: string;
  readonly email: string;
  readonly phone?: string;
  readonly address?: string;
  readonly city?: string;
  readonly state?: string;
  readonly country?: string;
  readonly postalCode?: string;
  readonly notes?: string;
}

// Room information
export interface CloudbedsReservationRoom {
  readonly roomTypeID: string;
  readonly startDate: string; // Format: YYYY-MM-DD
  readonly endDate: string;   // Format: YYYY-MM-DD
  readonly adults: number;
  readonly children?: number;
  readonly ratePlanID?: string;
  readonly notes?: string;
  readonly roomName?: string; // For dynamic room type resolution
}

// Complete reservation request
export interface CloudbedsReservationRequest {
  readonly propertyID: string;
  readonly sourceID?: string | number; // Format: 's-2-1'
  readonly guestData: CloudbedsReservationGuest;
  readonly roomsData: CloudbedsReservationRoom[];
  readonly status?: 'confirmed' | 'not_confirmed' | 'canceled';
  readonly thirdPartyIdentifier: string; // Unique identifier
  readonly sendEmailConfirmation: boolean;
  readonly startDate?: string; // Global start date
  readonly endDate?: string;   // Global end date
}

// API Response
export interface CloudbedsCreateReservationResponse {
  readonly success: boolean;
  readonly reservationID?: string;
  readonly confirmationCode?: string;
  readonly message?: string;
  readonly error?: {
    readonly code?: string;
    readonly message?: string;
  };
  readonly data?: {
    readonly reservationID?: string;
    readonly confirmationCode?: string;
  };
}
```

## API Endpoints

### 1. Create Reservation (Standard Method)
**Endpoint:** `POST /api/cloudbeds/reservation`

**Request Body:**
```json
{
  "propertyID": "317353",
  "guestData": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "address": "123 Main St",
    "city": "Madrid",
    "country": "ES",
    "postalCode": "28001"
  },
  "roomsData": [
    {
      "roomTypeID": "123456",
      "startDate": "2024-06-01",
      "endDate": "2024-06-03",
      "adults": 2,
      "children": 0,
      "roomName": "Garden Deluxe"
    }
  ],
  "status": "confirmed",
  "thirdPartyIdentifier": "unique-booking-id-123",
  "sendEmailConfirmation": true,
  "sourceID": "s-2-1"
}
```

### 2. Create Reservation (Direct Method)
**Endpoint:** `POST /api/cloudbeds/reservation/direct`

Uses FormData format for better compatibility with Cloudbeds API.

### 3. Generate Access Token
**Endpoint:** `POST /api/reservations/token`

**Request Body:**
```json
{
  "reservationId": "uuid-of-reservation"
}
```

**Response:**
```json
{
  "success": true,
  "token": "generated-access-token",
  "url": "/reservations/view?token=generated-access-token"
}
```

## Business Logic Implementation

### Reservation Creation Flow
1. **Validation** - Validate guest data and room information
2. **Property ID Resolution** - Ensure correct Cloudbeds property ID
3. **Room Type Resolution** - Map room names to Cloudbeds room type IDs
4. **API Call** - Send request to Cloudbeds API
5. **Database Storage** - Store reservation in local database
6. **Response** - Return reservation ID and confirmation code

### Key Validation Rules
- Guest first name, last name, and email are required
- At least one room must be specified
- Check-in and check-out dates are required
- Adults count must be at least 1
- Room type ID must be valid for the property

### Error Handling
- Invalid guest data: Return 400 with specific error message
- Missing room data: Return 400 with validation error
- Cloudbeds API errors: Return 400 with API error message
- Internal errors: Return 500 with generic error message

## Environment Variables Required

```env
# Cloudbeds API Configuration
CLOUDBEDS_API_KEY=your_api_key_here
CLOUDBEDS_CLIENT_ID=your_client_id_here
CLOUDBEDS_CLIENT_SECRET=your_client_secret_here
CLOUDBEDS_PROPERTY_ID=317353

# Supabase Configuration
PUBLIC_SUPABASE_URL=your_supabase_url
PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Authentication & Authorization

### Row Level Security (RLS)
The system uses Supabase RLS policies:

```sql
-- Users can view reservations of sites they have access to
CREATE POLICY "Los usuarios pueden ver las reservas de los sitios a los que tienen acceso"
  ON public.reservations
  FOR SELECT
  USING (has_site_access(site_id));

-- Admins can manage reservations
CREATE POLICY "Los administradores pueden gestionar reservas"
  ON public.reservations
  FOR ALL
  USING (is_site_admin(site_id));
```

### Helper Functions
```sql
-- Check if user has site access
CREATE OR REPLACE FUNCTION public.has_site_access(site_id uuid)
RETURNS boolean AS $$
DECLARE
  has_access boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.site_users
    WHERE user_id = auth.uid()
    AND site_id = has_site_access.site_id
  ) INTO has_access;

  RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if user is site admin
CREATE OR REPLACE FUNCTION public.is_site_admin(site_id uuid)
RETURNS boolean AS $$
DECLARE
  is_admin boolean;
BEGIN
  SELECT EXISTS (
    SELECT 1
    FROM public.site_users su
    JOIN public.user_site_permissions usp ON su.id = usp.site_user_id
    WHERE su.user_id = auth.uid()
    AND su.site_id = is_site_admin.site_id
    AND usp.permission = 'admin'
  ) INTO is_admin;

  RETURN is_admin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## MCP Server Implementation Guidelines

### Required MCP Tools
1. **create_reservation** - Main reservation creation tool
2. **get_reservation** - Retrieve reservation by ID
3. **search_reservations** - Search reservations by criteria
4. **generate_access_token** - Generate temporary access tokens
5. **verify_access_token** - Verify and get reservation from token

### Tool Schemas
```json
{
  "name": "create_reservation",
  "description": "Create a new hotel reservation",
  "inputSchema": {
    "type": "object",
    "properties": {
      "guestData": {
        "type": "object",
        "properties": {
          "firstName": {"type": "string"},
          "lastName": {"type": "string"},
          "email": {"type": "string", "format": "email"},
          "phone": {"type": "string"},
          "address": {"type": "string"},
          "city": {"type": "string"},
          "country": {"type": "string"},
          "postalCode": {"type": "string"}
        },
        "required": ["firstName", "lastName", "email"]
      },
      "roomsData": {
        "type": "array",
        "items": {
          "type": "object",
          "properties": {
            "roomTypeID": {"type": "string"},
            "roomName": {"type": "string"},
            "startDate": {"type": "string", "format": "date"},
            "endDate": {"type": "string", "format": "date"},
            "adults": {"type": "integer", "minimum": 1},
            "children": {"type": "integer", "minimum": 0}
          },
          "required": ["startDate", "endDate", "adults"]
        },
        "minItems": 1
      },
      "propertyID": {"type": "string"},
      "status": {"type": "string", "enum": ["confirmed", "not_confirmed", "canceled"]},
      "sendEmailConfirmation": {"type": "boolean"}
    },
    "required": ["guestData", "roomsData"]
  }
}
```

### Implementation Notes
- Use the existing API endpoints as the foundation
- Implement proper error handling and validation
- Support both direct and standard reservation creation methods
- Include room type resolution for dynamic room mapping
- Implement token-based access for reservation viewing
- Follow the existing authentication and authorization patterns

This technical specification provides all the necessary information to create an MCP server that can effectively create and manage reservations using the existing codebase architecture.

## Service Implementation Details

### Cloudbeds API Service Class
The main service class `CloudbedsApiService` provides two methods for creating reservations:

1. **createReservation()** - Standard JSON-based approach
2. **createReservationDirect()** - FormData-based approach (more reliable)

### Key Implementation Functions

#### Reservation Services (`src/lib/services/reservations.ts`)
```typescript
// Get user reservations
export async function getUserReservations(
  supabase: SupabaseClient,
  userId: string
): Promise<{data: Reservation[] | null; error: string | null}>

// Search reservations by email and confirmation code
export async function searchReservationByEmailAndCode(
  supabase: SupabaseClient,
  email: string,
  confirmationCode: string
): Promise<{data: Reservation[] | null; error: string | null}>

// Get reservation by ID
export async function getReservationById(
  supabase: SupabaseClient,
  reservationId: string
): Promise<{data: Reservation | null; error: string | null}>

// Generate temporary access token
export async function generateReservationAccessToken(
  supabase: SupabaseClient,
  reservationId: string
): Promise<{token: string | null; error: string | null}>

// Verify access token
export async function verifyReservationAccessToken(
  supabase: SupabaseClient,
  token: string
): Promise<{reservationId: string | null; error: string | null}>
```

### Critical Configuration Details

#### Cloudbeds API Authentication
- Uses API Key authentication with `x-api-key` header
- Supports OAuth2 with Bearer tokens as fallback
- Property ID must be included in headers as `X-PROPERTY-ID`

#### Data Format Requirements
- Dates must be in `YYYY-MM-DD` format
- Source ID should follow pattern `s-{id}-1` (e.g., `s-2-1`)
- Third-party identifier must be unique across all reservations
- Phone numbers should include country code

#### Room Type Resolution
The system includes dynamic room type resolution:
- Maps room names to Cloudbeds room type IDs
- Caches room types for performance
- Supports fuzzy matching for room names

### Error Handling Patterns

#### Common Error Scenarios
1. **Invalid Property Access** - User doesn't have access to property ID
2. **Room Type Not Found** - Invalid or unmapped room type ID
3. **Date Validation** - Invalid date formats or past dates
4. **Guest Data Validation** - Missing required guest information
5. **API Rate Limits** - Cloudbeds API throttling

#### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message"
  }
}
```

### Testing Examples

#### Successful Reservation Creation
```javascript
const reservationData = {
  propertyID: "317353",
  guestData: {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+34123456789",
    address: "Calle Mayor 123",
    city: "Madrid",
    country: "ES",
    postalCode: "28001"
  },
  roomsData: [{
    roomTypeID: "123456",
    roomName: "Garden Deluxe",
    startDate: "2024-06-01",
    endDate: "2024-06-03",
    adults: 2,
    children: 0
  }],
  status: "confirmed",
  thirdPartyIdentifier: `mcp-${Date.now()}`,
  sendEmailConfirmation: true,
  sourceID: "s-2-1"
};
```

#### Expected Response
```json
{
  "success": true,
  "reservationID": "CB123456789",
  "confirmationCode": "ABC123",
  "message": "Reserva creada exitosamente"
}
```

### Database Operations

#### Creating a Reservation Record
After successful Cloudbeds API call, store in local database:

```sql
INSERT INTO public.reservations (
  site_id,
  suite_id,
  user_id,
  cloudbeds_reservation_id,
  cloudbeds_confirmation_code,
  guest_name,
  guest_email,
  guest_phone,
  check_in_date,
  check_out_date,
  adults,
  children,
  status,
  total_price,
  currency,
  notes
) VALUES (
  $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
);
```

### Security Considerations

#### Input Validation
- Sanitize all user inputs
- Validate email formats
- Check date ranges (no past dates for check-in)
- Verify room capacity limits

#### Access Control
- Verify user has permission to create reservations for the site
- Check site-specific quotas and limits
- Validate room availability before creation

#### Data Privacy
- Store minimal guest information
- Use access tokens for temporary reservation viewing
- Implement proper data retention policies

### Performance Optimization

#### Caching Strategy
- Cache room types for 1 hour
- Cache property information
- Use connection pooling for database

#### Rate Limiting
- Implement client-side rate limiting
- Handle Cloudbeds API rate limits gracefully
- Use exponential backoff for retries

### Monitoring and Logging

#### Key Metrics to Track
- Reservation creation success rate
- API response times
- Error rates by type
- Room type resolution accuracy

#### Logging Requirements
- Log all API requests/responses
- Track reservation creation flow
- Monitor authentication failures
- Alert on high error rates

This comprehensive technical documentation provides everything needed to implement a robust MCP server for reservation creation.
