"""
Test directly with the API to isolate the payment method issue.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_direct_api_payment():
    """Test payment methods directly with the API."""
    print("🎯 Testing Payment Methods Directly with API...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base form data that we know works (gets to payment method requirement)
    base_form_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"direct.api.{int(asyncio.get_event_loop().time())}@example.com",
        "guestPhone": "+34123456789",
        "guestCountry": "ES",  # Make sure country is valid
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-direct-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": "true",
        "sourceID": "s-2-1",
        "rooms": json.dumps([{
            "roomTypeID": "653498",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "adults": 2,
            "children": 0
        }])
    }
    
    # Test different payment method values
    payment_methods_to_test = [
        "noPayment",
        "credit", 
        "no_payment",  # Try with underscore
        "no-payment",  # Try with dash
        "nopayment",   # Try lowercase
        "NOPAYMENT",   # Try uppercase
        "Credit",      # Try capitalized
        "CREDIT",      # Try uppercase
        "0",           # Try numeric
        "1",
        "none",        # Try other variations
        "cash",
        "card"
    ]
    
    try:
        async with httpx.AsyncClient() as client:
            # First, confirm we get to payment method requirement
            print(f"\n📡 Testing base data without payment method...")
            response = await client.post(f"{API_BASE_URL}/postReservation", data=base_form_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ Confirmed: Base data works, payment method required")
                    
                    # Now test each payment method
                    for payment_method in payment_methods_to_test:
                        print(f"\n🔄 Testing paymentMethod='{payment_method}'...")
                        
                        test_data = base_form_data.copy()
                        test_data["paymentMethod"] = payment_method
                        test_data["guestEmail"] = f"payment.{payment_method}.{int(asyncio.get_event_loop().time())}@example.com"
                        test_data["thirdPartyIdentifier"] = f"mcp-{payment_method}-{int(asyncio.get_event_loop().time())}"
                        
                        response2 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                        if response2.status_code == 200:
                            data2 = response2.json()
                            print(f"      Success: {data2.get('success')}")
                            print(f"      Message: {data2.get('message')}")
                            
                            if data2.get('success'):
                                print(f"\n🎉 SUCCESS! Payment method '{payment_method}' works!")
                                print(f"      Reservation ID: {data2.get('reservationID')}")
                                print(f"      Confirmation Code: {data2.get('confirmationCode')}")
                                return payment_method
                            elif "Invalid Parameter Format" not in data2.get('message', ''):
                                print(f"      ✅ Payment method '{payment_method}' accepted (different error)")
                                print(f"      New error: {data2.get('message')}")
                        else:
                            print(f"      HTTP Error: {response2.status_code}")
                else:
                    print(f"   ❌ Unexpected base response: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return None

async def test_without_payment_method():
    """Test if we can create reservation without payment method at all."""
    print(f"\n" + "=" * 70)
    print("🎯 Testing Without Payment Method Parameter...")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Try without payment method parameter at all
    form_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "Maria",
        "guestLastName": "Garcia",
        "guestEmail": f"no.payment.param.{int(asyncio.get_event_loop().time())}@example.com",
        "guestPhone": "+34123456789",
        "guestCountry": "ES",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-no-payment-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": "true",
        "sourceID": "s-2-1",
        "rooms": json.dumps([{
            "roomTypeID": "653498",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "adults": 2,
            "children": 0
        }])
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"\n📡 Testing without paymentMethod parameter...")
            response = await client.post(f"{API_BASE_URL}/postReservation", data=form_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"\n🎉 SUCCESS! No payment method needed!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    return True
                else:
                    print(f"   📝 Error without payment method: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    working_payment_method = asyncio.run(test_direct_api_payment())
    no_payment_works = asyncio.run(test_without_payment_method())
    
    if working_payment_method:
        print(f"\n🎉 FOUND WORKING PAYMENT METHOD: '{working_payment_method}'")
    elif no_payment_works:
        print(f"\n🎉 NO PAYMENT METHOD NEEDED!")
    else:
        print(f"\n🔧 PAYMENT METHOD ISSUE PERSISTS")
