"""
Compare direct API call vs MCP call with identical data.
"""

import asyncio
import httpx
import json
import os
from datetime import datetime, timedelta
from fastmcp import Client
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_direct_vs_mcp():
    """Compare direct API call vs MCP call."""
    print("🎯 Direct API vs MCP Comparison")
    print("=" * 50)
    
    tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
    
    # Test 1: Direct API call (we know this structure works)
    print(f"\n📡 Test 1: Direct API Call")
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # This exact structure worked in our previous tests
    direct_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "John",
        "guestLastName": "Doe",
        "guestEmail": f"direct.comparison.{int(asyncio.get_event_loop().time())}@example.com",
        "guestCountry": "ES",
        "startDate": tomorrow,
        "endDate": day_after,
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "rooms": json.dumps([{"roomTypeID": "653498"}]),  # Minimal rooms that worked
        "paymentMethod": "noPayment"
    }
    
    try:
        async with httpx.AsyncClient() as client:
            print(f"   Sending direct API request...")
            response = await client.post(f"{API_BASE_URL}/postReservation", data=direct_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if data.get('success'):
                    print(f"   🎉 DIRECT API SUCCESS!")
                    print(f"   Reservation ID: {data.get('reservationID')}")
                    direct_success = True
                else:
                    print(f"   ❌ Direct API failed: {data.get('message')}")
                    direct_success = False
            else:
                print(f"   HTTP Error: {response.status_code}")
                direct_success = False
                
    except Exception as e:
        print(f"   💥 Direct API Exception: {e}")
        direct_success = False
    
    # Test 2: MCP call with equivalent data
    print(f"\n📡 Test 2: MCP Call with Equivalent Data")
    
    # Convert direct API data to MCP format
    mcp_data = {
        "firstName": "John",
        "lastName": "Doe", 
        "email": f"mcp.comparison.{int(asyncio.get_event_loop().time())}@example.com",
        "country": "ES",
        "roomTypeID": "653498",
        "startDate": tomorrow,
        "endDate": day_after,
        "adults": 2,  # MCP expects integer
        "children": 0,  # MCP expects integer
        "paymentMethod": "noPayment"
    }
    
    try:
        client = Client("http://localhost:8000/mcp")
        async with client:
            print(f"   Sending MCP request...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": mcp_data
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"   Success: {response_data.get('success')}")
                print(f"   Message: {response_data.get('message')}")
                
                if response_data.get('success'):
                    print(f"   🎉 MCP SUCCESS!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    mcp_success = True
                else:
                    print(f"   ❌ MCP failed: {response_data.get('message')}")
                    mcp_success = False
            else:
                print(f"   ❌ MCP no response")
                mcp_success = False
                
    except Exception as e:
        print(f"   💥 MCP Exception: {e}")
        mcp_success = False
    
    # Test 3: If direct works but MCP doesn't, try minimal MCP data
    if direct_success and not mcp_success:
        print(f"\n📡 Test 3: Minimal MCP Data (since direct worked)")
        
        minimal_mcp = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"minimal.mcp.{int(asyncio.get_event_loop().time())}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after
        }
        
        try:
            client = Client("http://localhost:8000/mcp")
            async with client:
                print(f"   Sending minimal MCP request...")
                result = await client.call_tool("create_reservation_tool", {
                    "reservation_data": minimal_mcp
                })
                
                if result and result[0].text:
                    response_data = json.loads(result[0].text)
                    print(f"   Success: {response_data.get('success')}")
                    print(f"   Message: {response_data.get('message')}")
                    
                    if response_data.get('success'):
                        print(f"   🎉 MINIMAL MCP SUCCESS!")
                        return True
                    elif "paymentMethod is required" in response_data.get('message', ''):
                        print(f"   ✅ Minimal MCP works, just needs payment method")
                        
                        # Add payment method to minimal
                        minimal_mcp['paymentMethod'] = 'noPayment'
                        minimal_mcp['email'] = f"minimal.payment.{int(asyncio.get_event_loop().time())}@example.com"
                        
                        result2 = await client.call_tool("create_reservation_tool", {
                            "reservation_data": minimal_mcp
                        })
                        
                        if result2 and result2[0].text:
                            response_data2 = json.loads(result2[0].text)
                            if response_data2.get('success'):
                                print(f"   🎉 MINIMAL MCP + PAYMENT SUCCESS!")
                                return True
                            else:
                                print(f"   ❌ Minimal + payment failed: {response_data2.get('message')}")
                        
        except Exception as e:
            print(f"   💥 Minimal MCP Exception: {e}")
    
    # Summary
    print(f"\n📊 Summary:")
    print(f"   Direct API: {'✅ SUCCESS' if direct_success else '❌ FAILED'}")
    print(f"   MCP Call:   {'✅ SUCCESS' if mcp_success else '❌ FAILED'}")
    
    if direct_success and not mcp_success:
        print(f"\n💡 Direct API works but MCP doesn't - there's a processing issue in our MCP implementation")
    elif not direct_success and not mcp_success:
        print(f"\n💡 Both fail - the API structure itself needs work")
    elif direct_success and mcp_success:
        print(f"\n🎉 Both work - SUCCESS!")
    
    return direct_success or mcp_success

if __name__ == "__main__":
    success = asyncio.run(test_direct_vs_mcp())
    if success:
        print(f"\n🎉 AT LEAST ONE METHOD WORKS!")
    else:
        print(f"\n🔧 BOTH METHODS NEED MORE WORK")
