"""
Test script to verify MCP tools work with real Cloudbeds data.

This script tests the MCP tools with actual API calls to verify data quality.
"""

import asyncio
import json
from datetime import datetime, timed<PERSON>ta
from fastmcp import Client

async def test_real_data():
    """Test MCP tools with real data and verify responses."""
    print("🔍 Testing Cloudbeds MCP Tools with Real Data")
    print("=" * 60)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Test room types
        print("\n🏨 Testing Room Types...")
        try:
            room_types = await client.call_tool("get_room_types_tool")
            print(f"✅ Found {len(room_types)} room types")
            if room_types:
                # Parse the text content to get actual data
                data = json.loads(room_types[0].text)
                for room_type in data[:2]:  # Show first 2
                    print(f"   - {room_type['roomTypeName']} ({room_type['roomTypeNameShort']})")
                    print(f"     Max Guests: {room_type['maxGuests']}, Units: {room_type['roomTypeUnits']}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test rooms
        print("\n🚪 Testing Rooms...")
        try:
            rooms = await client.call_tool("get_rooms_tool")
            print(f"✅ Retrieved rooms data")
            if rooms:
                data = json.loads(rooms[0].text)
                if data and len(data) > 0 and 'rooms' in data[0]:
                    room_list = data[0]['rooms']
                    print(f"   Found {len(room_list)} individual rooms")
                    for room in room_list[:3]:  # Show first 3
                        print(f"   - {room['roomName']} (ID: {room['roomID']})")
                        print(f"     Type: {room['roomTypeName']}, Max Guests: {room['maxGuests']}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test recent reservations
        print("\n📅 Testing Recent Reservations...")
        try:
            reservations = await client.call_tool("get_reservations_tool", {"days_back": 7})
            print(f"✅ Retrieved reservations data")
            if reservations:
                data = json.loads(reservations[0].text)
                print(f"   Found {len(data)} reservations in last 7 days")
                for reservation in data[:3]:  # Show first 3
                    print(f"   - {reservation['guestName']} (ID: {reservation['reservationID']})")
                    print(f"     Dates: {reservation['startDate']} to {reservation['endDate']}")
                    print(f"     Status: {reservation['status']}, Balance: ${reservation['balance']}")
                
                # Test getting details for a specific reservation
                if data:
                    test_reservation_id = data[0]['reservationID']
                    print(f"\n🔍 Testing specific reservation details for ID: {test_reservation_id}")
                    try:
                        details = await client.call_tool("get_reservation_tool", {"reservation_id": test_reservation_id})
                        if details and details[0].text:
                            detail_data = json.loads(details[0].text)
                            if detail_data:
                                print(f"   ✅ Retrieved detailed reservation data")
                                print(f"   Guest: {detail_data.get('guestName', 'N/A')}")
                                print(f"   Property: {detail_data.get('propertyID', 'N/A')}")
                            else:
                                print(f"   ⚠️  No detailed data returned")
                    except Exception as e:
                        print(f"   ❌ Error getting reservation details: {e}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test availability
        print("\n📊 Testing Availability...")
        try:
            start_date = datetime.now().strftime('%Y-%m-%d')
            end_date = (datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d')
            availability = await client.call_tool("get_availability_tool", {
                "start_date": start_date,
                "end_date": end_date
            })
            print(f"✅ Retrieved availability data for {start_date} to {end_date}")
            if availability and availability[0].text:
                data = json.loads(availability[0].text)
                print(f"   Availability data length: {len(data)}")
                if not data:
                    print("   ℹ️  No availability data returned (this might be normal)")
            else:
                print("   ℹ️  Empty availability response")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test guest search
        print("\n👤 Testing Guest Search...")
        try:
            guests = await client.call_tool("search_guests_tool", {"search_term": "test"})
            print(f"✅ Searched for guests with term 'test'")
            if guests and guests[0].text:
                data = json.loads(guests[0].text)
                print(f"   Found {len(data)} matching guests")
                for guest in data[:2]:  # Show first 2
                    print(f"   - Guest ID: {guest.get('guestID', 'N/A')}")
            else:
                print("   ℹ️  No guests found matching 'test'")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test resources
        print("\n📚 Testing Resources...")
        try:
            # Test recent reservations resource
            recent_res = await client.read_resource("cloudbeds://reservations/recent")
            print(f"✅ Read recent reservations resource")
            if recent_res and recent_res[0].text:
                data = json.loads(recent_res[0].text)
                print(f"   Resource returned {len(data)} recent reservations")
            
            # Test property resource
            property_info = await client.read_resource("cloudbeds://property")
            print(f"✅ Read property resource")
            if property_info and property_info[0].text:
                data = json.loads(property_info[0].text)
                print(f"   Property data keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test prompts
        print("\n💬 Testing Prompts...")
        try:
            # Test new reservation prompt
            new_res_prompt = await client.get_prompt("new_reservation_prompt")
            print(f"✅ Retrieved new reservation prompt")
            if new_res_prompt and new_res_prompt.messages:
                print(f"   Prompt has {len(new_res_prompt.messages)} messages")
                print(f"   First message preview: {new_res_prompt.messages[0].content.text[:100]}...")
            
            # Test summarize reservation prompt with sample data
            sample_data = {
                "reservationID": "12345",
                "guestName": "John Doe",
                "startDate": "2025-06-01",
                "endDate": "2025-06-03",
                "roomName": "Deluxe King",
                "status": "confirmed"
            }
            summary_prompt = await client.get_prompt("summarize_reservation", {
                "reservation_data": json.dumps(sample_data)
            })
            print(f"✅ Retrieved summarize reservation prompt")
            if summary_prompt and summary_prompt.messages:
                print(f"   Summary prompt has {len(summary_prompt.messages)} messages")
                print(f"   Contains reservation ID: {'12345' in summary_prompt.messages[0].content.text}")
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_real_data())
