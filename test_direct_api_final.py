"""
Test directly with the API to isolate the payment method issue completely.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_direct_api_final():
    """Test payment methods directly with the API."""
    print("🎯 Testing Payment Methods Directly with API (Final)")
    print("=" * 70)
    
    headers = {
        "x-api-key": CLOUDBEDS_API_KEY,
        "X-PROPERTY-ID": CLOUDBEDS_PROPERTY_ID,
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    # Base form data that we know works (gets to payment method requirement)
    base_form_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"direct.final.{int(asyncio.get_event_loop().time())}@example.com",
        "guestPhone": "+34123456789",
        "guestCountry": "ES",  # Valid country
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "adults": "2",
        "children": "0",
        "roomTypeID": "653498",
        "status": "confirmed",
        "thirdPartyIdentifier": f"mcp-direct-final-{int(asyncio.get_event_loop().time())}",
        "sendEmailConfirmation": "true",
        "sourceID": "s-2-1",
        "rooms": json.dumps([{
            "roomTypeID": "653498",
            "startDate": "2025-05-26",
            "endDate": "2025-05-27",
            "adults": 2,
            "children": 0
        }])
    }
    
    try:
        async with httpx.AsyncClient() as client:
            # First, confirm we get to payment method requirement
            print(f"\n📡 Step 1: Testing base data without payment method...")
            response = await client.post(f"{API_BASE_URL}/postReservation", data=base_form_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                print(f"   Success: {data.get('success')}")
                print(f"   Message: {data.get('message')}")
                
                if "paymentMethod is required" in data.get('message', ''):
                    print(f"   ✅ Confirmed: Base data works, payment method required")
                    
                    # Test with 'noPayment'
                    print(f"\n📡 Step 2: Testing with paymentMethod='noPayment'...")
                    test_data_no_payment = base_form_data.copy()
                    test_data_no_payment["paymentMethod"] = "noPayment"
                    test_data_no_payment["guestEmail"] = f"nopayment.final.{int(asyncio.get_event_loop().time())}@example.com"
                    test_data_no_payment["thirdPartyIdentifier"] = f"mcp-nopayment-{int(asyncio.get_event_loop().time())}"
                    
                    response2 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data_no_payment, headers=headers)
                    if response2.status_code == 200:
                        data2 = response2.json()
                        print(f"      Success: {data2.get('success')}")
                        print(f"      Message: {data2.get('message')}")
                        
                        if data2.get('success'):
                            print(f"\n🎉 SUCCESS! 'noPayment' works!")
                            print(f"      Reservation ID: {data2.get('reservationID')}")
                            print(f"      Confirmation Code: {data2.get('confirmationCode')}")
                            return True
                        else:
                            print(f"      ❌ 'noPayment' failed: {data2.get('message')}")
                    
                    # Test with 'credit'
                    print(f"\n📡 Step 3: Testing with paymentMethod='credit'...")
                    test_data_credit = base_form_data.copy()
                    test_data_credit["paymentMethod"] = "credit"
                    test_data_credit["guestEmail"] = f"credit.final.{int(asyncio.get_event_loop().time())}@example.com"
                    test_data_credit["thirdPartyIdentifier"] = f"mcp-credit-{int(asyncio.get_event_loop().time())}"
                    
                    response3 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data_credit, headers=headers)
                    if response3.status_code == 200:
                        data3 = response3.json()
                        print(f"      Success: {data3.get('success')}")
                        print(f"      Message: {data3.get('message')}")
                        
                        if data3.get('success'):
                            print(f"\n🎉 SUCCESS! 'credit' works!")
                            print(f"      Reservation ID: {data3.get('reservationID')}")
                            print(f"      Confirmation Code: {data3.get('confirmationCode')}")
                            return True
                        else:
                            print(f"      ❌ 'credit' failed: {data3.get('message')}")
                    
                    # Test with other variations
                    print(f"\n📡 Step 4: Testing other payment method variations...")
                    other_methods = [
                        "no_payment",
                        "nopayment", 
                        "NOPAYMENT",
                        "noPayment",  # Try again with exact case
                        "Credit",
                        "CREDIT",
                        "cash",
                        "0",
                        "1",
                        "",  # Empty string
                    ]
                    
                    for method in other_methods:
                        print(f"\n   🔄 Testing '{method}'...")
                        test_data = base_form_data.copy()
                        test_data["paymentMethod"] = method
                        test_data["guestEmail"] = f"{method}.test.{int(asyncio.get_event_loop().time())}@example.com"
                        test_data["thirdPartyIdentifier"] = f"mcp-{method}-{int(asyncio.get_event_loop().time())}"
                        
                        response4 = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                        if response4.status_code == 200:
                            data4 = response4.json()
                            if data4.get('success'):
                                print(f"      🎉 SUCCESS! '{method}' works!")
                                print(f"      Reservation ID: {data4.get('reservationID')}")
                                return True
                            else:
                                print(f"      Message: {data4.get('message')}")
                        else:
                            print(f"      HTTP Error: {response4.status_code}")
                
                else:
                    print(f"   ❌ Unexpected base response: {data.get('message')}")
            else:
                print(f"   HTTP Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
    except Exception as e:
        print(f"   💥 Exception: {e}")
    
    return False

if __name__ == "__main__":
    success = asyncio.run(test_direct_api_final())
    if success:
        print(f"\n🎉🎉🎉 FOUND WORKING PAYMENT METHOD! 🎉🎉🎉")
        print(f"🚀 DIRECT API CALL SUCCESSFUL!")
    else:
        print(f"\n🔧 PAYMENT METHOD ISSUE PERSISTS IN DIRECT API CALLS")
