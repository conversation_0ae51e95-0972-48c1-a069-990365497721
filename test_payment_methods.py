"""
Test different payment methods.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_payment_methods():
    """Test different payment methods."""
    print("💳 Testing Different Payment Methods...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    rooms_json = json.dumps([{
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0"
    }])
    
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"payment.test.{int(asyncio.get_event_loop().time())}@example.com",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": rooms_json
    }
    
    # Test different payment methods
    payment_methods = [
        "Cash",
        "cash", 
        "Credit Card",
        "credit_card",
        "Visa",
        "visa",
        "Direct",
        "direct",
        "None",
        "none",
        "",  # Empty string
        "1",  # Numeric ID
        "2"
    ]
    
    for payment_method in payment_methods:
        print(f"\n📡 Testing payment method: '{payment_method}'")
        try:
            test_data = base_data.copy()
            if payment_method != "":  # Don't add empty string
                test_data["paymentMethod"] = payment_method
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Payment method '{payment_method}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        return payment_method
                    elif "paymentMethod" not in data.get('message', ''):
                        print(f"   ✅ Payment method accepted, other issue: {data.get('message')}")
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No valid payment method found")
    return None

if __name__ == "__main__":
    asyncio.run(test_payment_methods())
