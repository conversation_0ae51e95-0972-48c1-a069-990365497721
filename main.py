"""
Cloudbeds MCP main module.

This module runs the FastMCP server for Cloudbeds API.
"""

import argparse
from src.server import mcp

def main():
    """Run the Cloudbeds MCP server."""
    parser = argparse.ArgumentParser(description="Run the Cloudbeds MCP server")
    parser.add_argument("--transport", type=str, default="stdio",
                        choices=["stdio", "streamable-http", "sse"],
                        help="Transport type (stdio, streamable-http, sse)")
    parser.add_argument("--host", type=str, default="localhost",
                        help="Host for HTTP transport")
    parser.add_argument("--port", type=int, default=8000,
                        help="Port for HTTP transport")
    parser.add_argument("--path", type=str, default="/mcp",
                        help="Path for HTTP transport")

    args = parser.parse_args()

    print(f"Starting Cloudbeds MCP server with {args.transport} transport...")

    if args.transport == "stdio":
        mcp.run()
    elif args.transport == "streamable-http":
        mcp.run(transport="streamable-http", host=args.host, port=args.port, path=args.path)
    elif args.transport == "sse":
        mcp.run(transport="sse", host=args.host, port=args.port, path=args.path)

if __name__ == "__main__":
    main()
