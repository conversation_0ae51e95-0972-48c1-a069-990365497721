"""
Check valid IDs for reservation creation.
"""

import async<PERSON>
import json
from fastmcp import Client

async def check_valid_ids():
    """Check what valid IDs we have available."""
    print("🔍 Checking Valid IDs for Reservation Creation...")
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Get room types and their IDs
        print("\n🏨 Room Types:")
        try:
            room_types = await client.call_tool("get_room_types_tool")
            if room_types and room_types[0].text:
                room_data = json.loads(room_types[0].text)
                for room_type in room_data:
                    print(f"   - ID: {room_type['roomTypeID']}, Name: {room_type['roomTypeName']}")
                    print(f"     Short: {room_type['roomTypeNameShort']}, Units: {room_type['roomTypeUnits']}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Get individual rooms and their IDs
        print("\n🚪 Individual Rooms:")
        try:
            rooms = await client.call_tool("get_rooms_tool")
            if rooms and rooms[0].text:
                room_data = json.loads(rooms[0].text)
                if room_data and len(room_data) > 0 and 'rooms' in room_data[0]:
                    room_list = room_data[0]['rooms']
                    for room in room_list[:5]:  # Show first 5
                        print(f"   - Room ID: {room['roomID']}, Name: {room['roomName']}")
                        print(f"     Type ID: {room['roomTypeID']}, Type: {room['roomTypeName']}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        # Get recent reservations to see what parameters they have
        print("\n📅 Recent Reservations (for parameter reference):")
        try:
            reservations = await client.call_tool("get_reservations_tool", {"days_back": 3})
            if reservations and reservations[0].text:
                reservation_data = json.loads(reservations[0].text)
                if reservation_data:
                    sample_reservation = reservation_data[0]
                    print(f"   Sample Reservation ID: {sample_reservation['reservationID']}")
                    print(f"   Property ID: {sample_reservation.get('propertyID', 'Not shown')}")
                    print(f"   Room Type ID: {sample_reservation.get('roomTypeID', 'Not shown')}")
                    print(f"   Room ID: {sample_reservation.get('roomID', 'Not shown')}")
                    print(f"   Source: {sample_reservation.get('source', 'Not shown')}")
                    print(f"   Status: {sample_reservation.get('status', 'Not shown')}")
                    
                    # Show all keys to see what parameters exist
                    print(f"   All keys in reservation: {list(sample_reservation.keys())}")
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_valid_ids())
