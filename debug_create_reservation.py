"""
Debug script to test create_reservation with detailed logging.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def debug_create_reservation():
    """Debug the create_reservation function with detailed logging."""
    print("🔍 Debugging Create Reservation...")
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        # Get room types first
        try:
            room_types = await client.call_tool("get_room_types_tool")
            if room_types and room_types[0].text:
                room_data = json.loads(room_types[0].text)
                if room_data:
                    room_type_id = room_data[0]['roomTypeID']
                    print(f"✅ Using roomTypeID: {room_type_id}")
                else:
                    room_type_id = "653496"
                    print(f"⚠️  Using fallback roomTypeID: {room_type_id}")
            else:
                room_type_id = "653496"
                print(f"⚠️  Using fallback roomTypeID: {room_type_id}")
        except Exception as e:
            room_type_id = "653496"
            print(f"❌ Error getting room types: {e}")
        
        # Create minimal reservation data
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        minimal_reservation = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"test.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "startDate": tomorrow,
            "endDate": day_after,
            "roomTypeID": room_type_id,
            "guestCountry": "US",
            "paymentMethod": "cash"
        }
        
        print(f"\n📋 Reservation Data:")
        for key, value in minimal_reservation.items():
            print(f"   {key}: {value}")
        
        try:
            print(f"\n🚀 Calling create_reservation_tool...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": minimal_reservation
            })
            
            print(f"\n📥 Raw Result:")
            print(f"   Type: {type(result)}")
            print(f"   Length: {len(result) if result else 'None'}")
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Parsed Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success') is False:
                    print(f"\n❌ API Error: {response_data.get('message')}")
                else:
                    print(f"\n✅ Success! Response: {response_data}")
            else:
                print(f"\n⚠️  Empty or invalid response")
                
        except Exception as e:
            print(f"\n💥 Exception: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_create_reservation())
