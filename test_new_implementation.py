"""
Test the new implementation based on codebase analysis.
"""

import asyncio
import json
from datetime import datetime, timedelta
from fastmcp import Client

async def test_new_implementation():
    """Test the new implementation with correct structure."""
    print("🎯 Testing New Implementation Based on Codebase Analysis")
    print("=" * 70)
    
    client = Client("http://localhost:8000/mcp")
    
    async with client:
        tomorrow = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
        day_after = (datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')
        
        # Test 1: Simple reservation with new structure
        print(f"\n📋 Test 1: Simple reservation with new FormData structure")
        
        simple_reservation = {
            "firstName": "John",
            "lastName": "Doe",
            "email": f"new.impl.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "country": "ES",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 0,
            "paymentMethod": "credit",  # Using credit as default
            "sourceID": "2"  # Will be formatted to s-2-1
        }
        
        try:
            print(f"\n🚀 Creating reservation with new implementation...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": simple_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! New implementation works!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    
                    # Verify the reservation
                    if response_data.get('reservationID'):
                        print(f"\n🔍 Verifying reservation...")
                        try:
                            verify_result = await client.call_tool("get_reservation_tool", {
                                "reservation_id": response_data['reservationID']
                            })
                            if verify_result and verify_result[0].text:
                                verify_data = json.loads(verify_result[0].text)
                                if verify_data.get('success') is not False:
                                    print(f"   ✅ Reservation verified! Guest: {verify_data.get('guestName', 'N/A')}")
                                else:
                                    print(f"   ⚠️  Verification failed: {verify_data.get('message')}")
                        except Exception as ve:
                            print(f"   ⚠️  Verification error: {ve}")
                    
                    return True
                else:
                    print(f"\n❌ Failed: {response_data.get('message')}")
                    
                    # Try with noPayment if credit failed
                    if "paymentMethod" in response_data.get('message', '') or "Invalid Parameter Format" in response_data.get('message', ''):
                        print(f"\n🔄 Trying with paymentMethod='noPayment'...")
                        simple_reservation['paymentMethod'] = 'noPayment'
                        simple_reservation['email'] = f"nopay.impl.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com"
                        
                        result2 = await client.call_tool("create_reservation_tool", {
                            "reservation_data": simple_reservation
                        })
                        
                        if result2 and result2[0].text:
                            response_data2 = json.loads(result2[0].text)
                            if response_data2.get('success'):
                                print(f"   🎉 SUCCESS with 'noPayment'!")
                                return True
                            else:
                                print(f"   ❌ Also failed with 'noPayment': {response_data2.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception: {str(e)}")
            import traceback
            traceback.print_exc()
        
        # Test 2: Complete reservation with all fields
        print(f"\n" + "=" * 70)
        print(f"📋 Test 2: Complete reservation with all fields")
        
        complete_reservation = {
            "firstName": "Maria",
            "lastName": "Garcia",
            "email": f"complete.new.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "phone": "+34666777888",
            "address": "Calle Gran Via 123",
            "city": "Madrid",
            "country": "ES",
            "postalCode": "28001",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after,
            "adults": 2,
            "children": 1,
            "status": "confirmed",
            "sendEmailConfirmation": True,
            "sourceID": "s-2-1",  # Already formatted
            "paymentMethod": "credit",
            "thirdPartyIdentifier": f"mcp-complete-new-{int(datetime.now().timestamp())}",
            "notes": "Complete test reservation with new implementation"
        }
        
        try:
            print(f"\n🚀 Creating complete reservation...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": complete_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Complete reservation created!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    print(f"   Confirmation Code: {response_data.get('confirmationCode')}")
                    print(f"   Third Party ID: {response_data.get('thirdPartyIdentifier')}")
                    return True
                else:
                    print(f"\n❌ Complete reservation failed: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with complete reservation: {str(e)}")
        
        # Test 3: Minimal reservation (just required fields)
        print(f"\n" + "=" * 70)
        print(f"📋 Test 3: Minimal reservation (required fields only)")
        
        minimal_reservation = {
            "firstName": "Carlos",
            "lastName": "Rodriguez",
            "email": f"minimal.new.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
            "roomTypeID": "653498",
            "startDate": tomorrow,
            "endDate": day_after
        }
        
        try:
            print(f"\n🚀 Creating minimal reservation...")
            result = await client.call_tool("create_reservation_tool", {
                "reservation_data": minimal_reservation
            })
            
            if result and result[0].text:
                response_data = json.loads(result[0].text)
                print(f"\n📊 Response:")
                print(json.dumps(response_data, indent=2))
                
                if response_data.get('success'):
                    print(f"\n🎉 SUCCESS! Minimal reservation created!")
                    print(f"   Reservation ID: {response_data.get('reservationID')}")
                    return True
                else:
                    print(f"\n📝 Minimal reservation result: {response_data.get('message')}")
                    
        except Exception as e:
            print(f"\n💥 Exception with minimal reservation: {str(e)}")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(test_new_implementation())
    if success:
        print(f"\n🎉🎉🎉 NEW IMPLEMENTATION SUCCESS! 🎉🎉🎉")
        print(f"🚀 CODEBASE ANALYSIS IMPLEMENTATION WORKS!")
        print(f"✅ MCP SERVER IS PRODUCTION READY!")
        print(f"🏆 IMPLEMENTATION COMPLETE!")
    else:
        print(f"\n🔧 NEW IMPLEMENTATION NEEDS MORE REFINEMENT...")
