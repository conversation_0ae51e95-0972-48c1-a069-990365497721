"""
Test different country codes.
"""

import asyncio
import httpx
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

CLOUDBEDS_API_KEY = os.getenv("CLOUDBEDS_API_KEY")
CLOUDBEDS_PROPERTY_ID = os.getenv("CLOUDBEDS_PROPERTY_ID")
API_BASE_URL = "https://hotels.cloudbeds.com/api/v1.2"

async def test_country_codes():
    """Test different country codes."""
    print("🌍 Testing Different Country Codes...")
    
    headers = {
        "Authorization": f"Bearer {CLOUDBEDS_API_KEY}",
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    rooms_json = json.dumps([{
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0"
    }])
    
    base_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "<PERSON>",
        "guestLastName": "Doe",
        "guestEmail": f"country.test.{int(asyncio.get_event_loop().time())}@example.com",
        "startDate": "2025-05-26",
        "endDate": "2025-05-27",
        "roomTypeID": "653498",
        "adults": "2",
        "children": "0",
        "rooms": rooms_json,
        "paymentMethod": "Cash"
    }
    
    # Test different country codes - including the one found in existing data
    country_codes = [
        "FR",  # Found in existing reservation
        "USA",
        "US", 
        "United States",
        "GB",
        "UK",
        "CA",
        "DE",
        "ES",
        "IT",
        "AU",
        "NZ",
        "JP",
        "BR",
        "MX"
    ]
    
    for country_code in country_codes:
        print(f"\n📡 Testing country code: '{country_code}'")
        try:
            test_data = base_data.copy()
            test_data["guestCountry"] = country_code
            test_data["guestEmail"] = f"country.{country_code.lower()}.{int(asyncio.get_event_loop().time())}@example.com"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{API_BASE_URL}/postReservation", data=test_data, headers=headers)
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"   Success: {data.get('success')}")
                    print(f"   Message: {data.get('message')}")
                    
                    if data.get('success'):
                        print(f"   🎉 SUCCESS! Country code '{country_code}' works!")
                        if 'reservationID' in data:
                            print(f"   Reservation ID: {data['reservationID']}")
                        print(f"   Full response: {json.dumps(data, indent=2)}")
                        return country_code
                    elif "guestCountry" not in data.get('message', ''):
                        print(f"   ✅ Country code accepted, other issue: {data.get('message')}")
                        # Continue to see what the next error is
                else:
                    print(f"   HTTP Error: {response.status_code}")
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
    
    print(f"\n❌ No valid country code found")
    return None

if __name__ == "__main__":
    asyncio.run(test_country_codes())
